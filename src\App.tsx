import React, { useState, useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ExtensionHeader } from './components/ExtensionHeader';
import { TabNavigation } from './components/TabNavigation';
import { FlightDeals } from './components/FlightDeals';
import { HotelDeals } from './components/HotelDeals';
import { SearchForm } from './components/SearchForm';
import { PriceTrackingDashboard } from './components/PriceTrackingDashboard';
import { useTravelStore, useTravelActions, useActiveTab, useIsLoading, useError } from './store/travel-store';

export function App() {
  const activeTab = useActiveTab();
  const isLoading = useIsLoading();
  const error = useError();
  const { setActiveTab, initializeServices, clearError } = useTravelActions();

  const [showPriceTracking, setShowPriceTracking] = useState(false);
  const [selectedSearchKey, setSelectedSearchKey] = useState<string | null>(null);
  const [selectedPrice, setSelectedPrice] = useState<number | null>(null);

  // Initialize services on app start
  useEffect(() => {
    initializeServices();
  }, [initializeServices]);

  // Handle error display
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  return (
    <div className="min-h-screen w-full bg-gray-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="w-[450px] h-[700px] bg-white rounded-lg shadow-xl overflow-hidden flex flex-col"
      >
        <div className="sticky top-0 z-20">
          <ExtensionHeader />
        </div>

        <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-red-50 border-l-4 border-red-400 p-4 mx-4 mt-2"
          >
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Loading Overlay */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-30"
          >
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF385C] mx-auto mb-2"></div>
              <p className="text-sm text-gray-600">Initializing TravelSaver...</p>
            </div>
          </motion.div>
        )}

        <div className="flex-1 overflow-y-auto">
          <div className="p-4 space-y-4">
            {/* Search Form */}
            <SearchForm type={activeTab} />

            {/* Main Content */}
            <AnimatePresence mode="wait">
              {showPriceTracking ? (
                <motion.div
                  key="price-tracking"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Price Tracking</h2>
                    <button
                      onClick={() => setShowPriceTracking(false)}
                      className="text-sm text-[#FF385C] hover:underline"
                    >
                      Back to Deals
                    </button>
                  </div>
                  <PriceTrackingDashboard
                    searchKey={selectedSearchKey || undefined}
                    currentPrice={selectedPrice || undefined}
                    currency="USD"
                  />
                </motion.div>
              ) : (
                <>
                  {activeTab === 'flights' ? (
                    <motion.div
                      key="flights"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      <FlightDeals
                        onTrackPrice={(searchKey: string, price: number) => {
                          setSelectedSearchKey(searchKey);
                          setSelectedPrice(price);
                          setShowPriceTracking(true);
                        }}
                      />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="hotels"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      <HotelDeals
                        onTrackPrice={(searchKey: string, price: number) => {
                          setSelectedSearchKey(searchKey);
                          setSelectedPrice(price);
                          setShowPriceTracking(true);
                        }}
                      />
                    </motion.div>
                  )}
                </>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.div>
    </div>
  );
}