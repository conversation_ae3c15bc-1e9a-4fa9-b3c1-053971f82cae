# TravelSaver - Smart Travel Deals Chrome Extension

A comprehensive Chrome extension that provides real-time price tracking, deal notifications, and affiliate integration for flights and hotels. Built with React, TypeScript, and modern web technologies.

## 🚀 Features

### Core Functionality
- **Real-time Price Tracking**: Monitor flight and hotel prices across multiple providers
- **Global Deal Discovery**: Find the cheapest flights and hotels worldwide
- **Location-based Services**: Automatic location detection for relevant local deals
- **Price Projections**: Historical trends and future price forecasts
- **Smart Notifications**: Real-time alerts for price drops and special offers

### Affiliate Integration
- **Booking.com Integration**: Seamless affiliate link management
- **Multi-partner Support**: Expedia, Hotels.com, and other major travel sites
- **Revenue Tracking**: Comprehensive analytics and performance metrics
- **Conversion Optimization**: Smart redirect system for maximum earnings

### User Experience
- **Price Comparison Dashboard**: Visual charts and trend analysis
- **Search Enhancement**: Advanced filtering and personalized recommendations
- **Content Script Integration**: Overlay functionality on partner sites
- **Responsive Design**: Optimized for all screen sizes

## 🛠️ Technical Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **State Management**: Zustand
- **Charts**: Chart.js with React integration
- **Animations**: Framer Motion
- **APIs**: Amadeus Travel API, Skyscanner, OpenWeather
- **Build Tool**: Vite
- **Extension**: Chrome Extension Manifest V3

## 📋 Prerequisites

- Node.js 16+ and npm
- Chrome browser for testing
- API keys for travel services (see Setup section)

## 🔧 Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd travel-saver-extension
npm install
```

### 2. Environment Configuration

Copy the example environment file and configure your API keys:

```bash
cp .env.example .env
```

Edit `.env` with your API credentials:

```env
# Required for flight/hotel search
AMADEUS_CLIENT_ID=your_amadeus_client_id
AMADEUS_CLIENT_SECRET=your_amadeus_client_secret

# Required for geolocation services
OPENWEATHER_API_KEY=your_openweather_api_key

# Required for affiliate tracking
BOOKING_AFFILIATE_ID=your_booking_affiliate_id
EXPEDIA_AFFILIATE_ID=your_expedia_affiliate_id
HOTELS_AFFILIATE_ID=your_hotels_affiliate_id
```

### 3. API Key Setup

#### Amadeus Travel API
1. Visit [Amadeus for Developers](https://developers.amadeus.com/)
2. Create a free account
3. Create a new application
4. Copy your Client ID and Client Secret

#### OpenWeather API
1. Visit [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for a free account
3. Generate an API key
4. Copy your API key

#### Affiliate Programs
1. **Booking.com**: Apply at [Booking.com Partner Hub](https://partner.booking.com/)
2. **Expedia**: Apply at [Expedia Partner Solutions](https://www.expediapartnersolutions.com/)
3. **Hotels.com**: Apply through Expedia Partner Solutions

## 🚀 Development

### Run Development Server
```bash
npm run dev
```
This starts the Vite development server at `http://localhost:5173`

### Build for Production
```bash
npm run build
```

### Build Chrome Extension
```bash
npm run build:extension
```
This creates a `dist-extension` folder ready for Chrome extension loading.

## 📦 Chrome Extension Installation

### Development Installation
1. Build the extension: `npm run build:extension`
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" (top right toggle)
4. Click "Load unpacked" and select the `dist-extension` folder
5. The TravelSaver extension should now appear in your extensions

### Testing the Extension
1. Click the TravelSaver icon in your Chrome toolbar
2. The extension popup should open with search functionality
3. Visit travel sites like Booking.com to see content script enhancements
4. Set up price alerts and test notifications

## 🏗️ Project Structure

```
src/
├── components/           # React components
│   ├── SearchForm.tsx           # Flight/hotel search interface
│   ├── PriceTrackingDashboard.tsx  # Price analysis and alerts
│   ├── FlightDeals.tsx          # Flight deals display
│   ├── HotelDeals.tsx           # Hotel deals display
│   └── ...
├── services/            # API and business logic
│   ├── travel-service.ts        # Main travel service orchestrator
│   ├── amadeus-api.ts           # Amadeus API integration
│   ├── price-tracking-service.ts # Price monitoring and alerts
│   ├── affiliate-service.ts     # Affiliate link management
│   └── geolocation-service.ts   # Location services
├── store/               # State management
│   └── travel-store.ts          # Zustand store
├── types/               # TypeScript definitions
│   └── travel.ts               # Travel-related types
├── background.ts        # Chrome extension background script
├── content.ts          # Content script for travel sites
└── App.tsx             # Main application component
```

## 🔌 API Integration

### Amadeus Travel API
- **Flight Search**: Real-time flight offers and pricing
- **Hotel Search**: Hotel availability and rates
- **Location Search**: Airport and city codes
- **Price Analysis**: Historical pricing data

### Geolocation Services
- **Browser Geolocation**: User's current location
- **IP Geolocation**: Fallback location detection
- **Reverse Geocoding**: Convert coordinates to city names

### Price Tracking
- **Historical Data**: Store and analyze price trends
- **Predictions**: Machine learning-based price forecasting
- **Alerts**: Real-time notifications for price changes

## 💰 Affiliate Integration

### Supported Partners
- **Booking.com**: Hotels and flights
- **Expedia**: Full travel booking platform
- **Hotels.com**: Hotel-focused bookings

### Revenue Tracking
- **Click Tracking**: Monitor affiliate link clicks
- **Conversion Tracking**: Track successful bookings
- **Performance Analytics**: Detailed revenue reports
- **Commission Optimization**: Smart partner selection

## 🔔 Notifications

### Price Alerts
- **Target Price Alerts**: Notify when prices drop below threshold
- **Trend Alerts**: Notify on significant price changes
- **Deal Alerts**: Notify about exclusive deals and offers

### Browser Notifications
- **Chrome Notifications API**: Native browser notifications
- **Persistent Alerts**: Background monitoring
- **Smart Timing**: Optimal notification scheduling

## 🎨 UI/UX Features

### Modern Design
- **Responsive Layout**: Works on all screen sizes
- **Dark/Light Mode**: Automatic theme detection
- **Smooth Animations**: Framer Motion animations
- **Intuitive Navigation**: Tab-based interface

### Data Visualization
- **Price Charts**: Interactive price history graphs
- **Trend Indicators**: Visual trend analysis
- **Forecast Charts**: Future price predictions
- **Comparison Tables**: Side-by-side price comparisons

## 🧪 Testing

### Manual Testing
1. Test search functionality with various destinations
2. Verify price tracking and alert creation
3. Test affiliate link generation and tracking
4. Verify content script functionality on travel sites

### API Testing
1. Verify Amadeus API integration
2. Test geolocation services
3. Validate price tracking accuracy
4. Test affiliate link generation

## 🚀 Deployment

### Chrome Web Store
1. Build production version: `npm run build:extension`
2. Create a ZIP file of the `dist-extension` folder
3. Upload to [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
4. Fill out store listing details
5. Submit for review

### Updates
1. Update version in `manifest.json` and `package.json`
2. Build new version
3. Upload updated ZIP to Chrome Web Store
4. Users will receive automatic updates

## 📊 Analytics and Monitoring

### Performance Metrics
- **Search Response Times**: API performance monitoring
- **User Engagement**: Feature usage analytics
- **Conversion Rates**: Affiliate performance tracking
- **Error Monitoring**: API and extension error tracking

### Revenue Analytics
- **Commission Tracking**: Real-time earnings data
- **Partner Performance**: Compare affiliate partners
- **User Value**: Lifetime value calculations
- **ROI Analysis**: Return on investment metrics

## 🔒 Privacy and Security

### Data Protection
- **Local Storage**: Sensitive data stored locally
- **API Security**: Secure API key management
- **User Privacy**: No personal data collection
- **GDPR Compliance**: European privacy regulations

### Security Features
- **Content Security Policy**: XSS protection
- **Secure Communications**: HTTPS-only API calls
- **Permission Management**: Minimal required permissions
- **Data Encryption**: Sensitive data encryption

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Common Issues
- **API Key Errors**: Verify all API keys are correctly configured
- **Extension Not Loading**: Check Chrome developer mode is enabled
- **Search Not Working**: Verify Amadeus API credentials
- **Notifications Not Showing**: Check Chrome notification permissions

### Getting Help
- Create an issue in the GitHub repository
- Check the troubleshooting section in the wiki
- Review API documentation for integration issues

## 🔮 Future Enhancements

### Planned Features
- **Multi-language Support**: International market expansion
- **Mobile App**: React Native companion app
- **Advanced ML**: Improved price prediction algorithms
- **Social Features**: Share deals with friends
- **Corporate Features**: Business travel management

### API Expansions
- **Additional Travel APIs**: Kayak, Priceline integration
- **Car Rental APIs**: Complete travel solution
- **Activity APIs**: Tours and experiences
- **Weather Integration**: Travel condition insights

---

**TravelSaver** - Making travel more affordable, one deal at a time! ✈️🏨💰
