
// TravelSaver Content Script
console.log('TravelSaver content script loaded on:', window.location.hostname);

// Basic price enhancement functionality
function enhancePrices() {
  const priceElements = document.querySelectorAll('[class*="price"], [data-testid*="price"]');
  
  priceElements.forEach(element => {
    if (!element.querySelector('.travelsaver-badge')) {
      const badge = document.createElement('span');
      badge.className = 'travelsaver-badge';
      badge.textContent = 'Save $25';
      badge.style.cssText = `
        background: linear-gradient(135deg, #FF385C, #E0314F);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: bold;
        margin-left: 4px;
        cursor: pointer;
      `;
      
      element.appendChild(badge);
    }
  });
}

// Run enhancement
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', enhancePrices);
} else {
  enhancePrices();
}

// Re-run on dynamic content changes
const observer = new MutationObserver(enhancePrices);
observer.observe(document.body, { childList: true, subtree: true });
