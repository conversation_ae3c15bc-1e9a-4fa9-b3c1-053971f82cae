import { ApiClient, API_ENDPOINTS, rateLimiter } from './api-client';
import { 
  FlightOffer, 
  HotelOffer, 
  FlightSearchCriteria, 
  HotelSearchCriteria, 
  Location,
  ApiResponse 
} from '../types/travel';

export class AmadeusAPI {
  private client: ApiClient;
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor(clientId: string, clientSecret: string) {
    this.client = new ApiClient(API_ENDPOINTS.AMADEUS.BASE_URL, '');
    this.authenticate(clientId, clientSecret);
  }

  private async authenticate(clientId: string, clientSecret: string): Promise<void> {
    try {
      const response = await this.client.post<{
        access_token: string;
        expires_in: number;
        token_type: string;
      }>(API_ENDPOINTS.AMADEUS.AUTH, {
        grant_type: 'client_credentials',
        client_id: clientId,
        client_secret: clientSecret
      });

      this.accessToken = response.access_token;
      this.tokenExpiry = Date.now() + (response.expires_in * 1000);
      this.client.updateApiKey(this.accessToken);
      
      console.log('Amadeus API authenticated successfully');
    } catch (error) {
      console.error('Amadeus authentication failed:', error);
      throw new Error('Failed to authenticate with Amadeus API');
    }
  }

  private async ensureValidToken(): Promise<void> {
    if (!this.accessToken || Date.now() >= this.tokenExpiry - 60000) {
      // Token expired or expires in less than 1 minute
      throw new Error('Token expired. Re-authentication required.');
    }
  }

  async searchFlights(criteria: FlightSearchCriteria): Promise<FlightOffer[]> {
    await this.ensureValidToken();
    
    if (!(await rateLimiter.checkLimit('amadeus'))) {
      throw new Error('Rate limit exceeded for Amadeus API');
    }

    try {
      const params = {
        originLocationCode: criteria.origin,
        destinationLocationCode: criteria.destination,
        departureDate: criteria.departureDate,
        adults: criteria.adults,
        ...(criteria.returnDate && { returnDate: criteria.returnDate }),
        ...(criteria.children && { children: criteria.children }),
        ...(criteria.infants && { infants: criteria.infants }),
        travelClass: criteria.travelClass,
        ...(criteria.nonStop && { nonStop: criteria.nonStop }),
        ...(criteria.maxPrice && { maxPrice: criteria.maxPrice }),
        max: 50 // Limit results
      };

      const response = await this.client.get<ApiResponse<FlightOffer[]>>(
        API_ENDPOINTS.AMADEUS.FLIGHT_OFFERS,
        { params }
      );

      return response.data || [];
    } catch (error) {
      console.error('Flight search failed:', error);
      throw new Error('Failed to search flights');
    }
  }

  async searchHotels(criteria: HotelSearchCriteria): Promise<HotelOffer[]> {
    await this.ensureValidToken();
    
    if (!(await rateLimiter.checkLimit('amadeus'))) {
      throw new Error('Rate limit exceeded for Amadeus API');
    }

    try {
      const params = {
        cityCode: criteria.destination,
        checkInDate: criteria.checkIn,
        checkOutDate: criteria.checkOut,
        adults: criteria.adults,
        ...(criteria.children && { children: criteria.children }),
        roomQuantity: criteria.rooms,
        ...(criteria.maxPrice && { priceRange: `0-${criteria.maxPrice}` }),
        ...(criteria.minRating && { ratings: criteria.minRating }),
        sort: 'PRICE',
        view: 'FULL'
      };

      const response = await this.client.get<ApiResponse<any[]>>(
        API_ENDPOINTS.AMADEUS.HOTEL_OFFERS,
        { params }
      );

      // Transform Amadeus hotel response to our HotelOffer format
      return this.transformHotelOffers(response.data || []);
    } catch (error) {
      console.error('Hotel search failed:', error);
      throw new Error('Failed to search hotels');
    }
  }

  async searchLocations(query: string, type: 'AIRPORT' | 'CITY' = 'AIRPORT'): Promise<Location[]> {
    await this.ensureValidToken();
    
    if (!(await rateLimiter.checkLimit('amadeus'))) {
      throw new Error('Rate limit exceeded for Amadeus API');
    }

    try {
      const params = {
        keyword: query,
        subType: type,
        'page[limit]': 10
      };

      const response = await this.client.get<ApiResponse<any[]>>(
        API_ENDPOINTS.AMADEUS.LOCATIONS,
        { params }
      );

      return this.transformLocations(response.data || []);
    } catch (error) {
      console.error('Location search failed:', error);
      throw new Error('Failed to search locations');
    }
  }

  async getFlightPricing(flightOffer: FlightOffer): Promise<FlightOffer> {
    await this.ensureValidToken();
    
    if (!(await rateLimiter.checkLimit('amadeus'))) {
      throw new Error('Rate limit exceeded for Amadeus API');
    }

    try {
      const response = await this.client.post<ApiResponse<FlightOffer>>(
        API_ENDPOINTS.AMADEUS.FLIGHT_PRICES,
        {
          data: {
            type: 'flight-offers-pricing',
            flightOffers: [flightOffer]
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Flight pricing failed:', error);
      throw new Error('Failed to get flight pricing');
    }
  }

  private transformHotelOffers(amadeusHotels: any[]): HotelOffer[] {
    return amadeusHotels.map(hotel => ({
      id: hotel.hotel?.hotelId || '',
      name: hotel.hotel?.name || '',
      location: {
        address: hotel.hotel?.address?.lines?.join(', ') || '',
        city: hotel.hotel?.address?.cityName || '',
        country: hotel.hotel?.address?.countryCode || '',
        coordinates: {
          latitude: parseFloat(hotel.hotel?.latitude) || 0,
          longitude: parseFloat(hotel.hotel?.longitude) || 0
        }
      },
      rating: hotel.hotel?.rating || 0,
      price: {
        total: parseFloat(hotel.offers?.[0]?.price?.total) || 0,
        currency: hotel.offers?.[0]?.price?.currency || 'USD',
        perNight: parseFloat(hotel.offers?.[0]?.price?.base) || 0
      },
      checkIn: hotel.offers?.[0]?.checkInDate || '',
      checkOut: hotel.offers?.[0]?.checkOutDate || '',
      nights: this.calculateNights(
        hotel.offers?.[0]?.checkInDate,
        hotel.offers?.[0]?.checkOutDate
      ),
      images: [],
      amenities: hotel.hotel?.amenities || [],
      description: hotel.hotel?.description?.text || ''
    }));
  }

  private transformLocations(amadeusLocations: any[]): Location[] {
    return amadeusLocations.map(location => ({
      code: location.iataCode || location.id,
      name: location.name,
      city: location.address?.cityName || '',
      country: location.address?.countryName || '',
      coordinates: location.geoCode ? {
        latitude: parseFloat(location.geoCode.latitude),
        longitude: parseFloat(location.geoCode.longitude)
      } : undefined
    }));
  }

  private calculateNights(checkIn: string, checkOut: string): number {
    if (!checkIn || !checkOut) return 1;
    
    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);
    const diffTime = Math.abs(checkOutDate.getTime() - checkInDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays || 1;
  }
}
