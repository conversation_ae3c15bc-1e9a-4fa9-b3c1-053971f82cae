import { 
  PriceHistory, 
  PriceAlert, 
  PriceNotification, 
  FlightSearchCriteria, 
  HotelSearchCriteria,
  FlightOffer,
  HotelOffer 
} from '../types/travel';

export class PriceTrackingService {
  private alerts: Map<string, PriceAlert> = new Map();
  private priceHistory: Map<string, PriceHistory[]> = new Map();
  private checkInterval: number = 30 * 60 * 1000; // 30 minutes

  constructor() {
    this.loadStoredData();
    this.startPriceMonitoring();
  }

  async createPriceAlert(
    type: 'flight' | 'hotel',
    criteria: FlightSearchCriteria | HotelSearchCriteria,
    targetPrice: number,
    userId?: string
  ): Promise<string> {
    const alertId = this.generateAlertId();
    
    const alert: PriceAlert = {
      id: alertId,
      userId,
      type,
      criteria,
      targetPrice,
      currentPrice: 0,
      isActive: true,
      createdAt: new Date().toISOString(),
      lastChecked: new Date().toISOString(),
      notifications: []
    };

    this.alerts.set(alertId, alert);
    await this.saveAlertsToStorage();
    
    // Immediately check current price
    await this.checkAlertPrice(alertId);
    
    return alertId;
  }

  async updatePriceAlert(alertId: string, updates: Partial<PriceAlert>): Promise<void> {
    const alert = this.alerts.get(alertId);
    if (!alert) {
      throw new Error('Price alert not found');
    }

    const updatedAlert = { ...alert, ...updates };
    this.alerts.set(alertId, updatedAlert);
    await this.saveAlertsToStorage();
  }

  async deletePriceAlert(alertId: string): Promise<void> {
    this.alerts.delete(alertId);
    await this.saveAlertsToStorage();
  }

  async getUserAlerts(userId?: string): Promise<PriceAlert[]> {
    const alerts = Array.from(this.alerts.values());
    return userId ? alerts.filter(alert => alert.userId === userId) : alerts;
  }

  async addPriceHistory(
    searchKey: string,
    price: number,
    currency: string,
    source: string
  ): Promise<void> {
    const history = this.priceHistory.get(searchKey) || [];
    
    const newEntry: PriceHistory = {
      date: new Date().toISOString(),
      price,
      currency,
      source
    };

    history.push(newEntry);
    
    // Keep only last 100 entries to manage storage
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }

    this.priceHistory.set(searchKey, history);
    await this.savePriceHistoryToStorage();
  }

  async getPriceHistory(searchKey: string): Promise<PriceHistory[]> {
    return this.priceHistory.get(searchKey) || [];
  }

  async analyzePriceTrends(searchKey: string): Promise<{
    trend: 'increasing' | 'decreasing' | 'stable';
    averagePrice: number;
    lowestPrice: number;
    highestPrice: number;
    priceChange24h: number;
    priceChange7d: number;
    recommendation: string;
  }> {
    const history = await this.getPriceHistory(searchKey);
    
    if (history.length < 2) {
      return {
        trend: 'stable',
        averagePrice: history[0]?.price || 0,
        lowestPrice: history[0]?.price || 0,
        highestPrice: history[0]?.price || 0,
        priceChange24h: 0,
        priceChange7d: 0,
        recommendation: 'Not enough data for analysis'
      };
    }

    const prices = history.map(h => h.price);
    const dates = history.map(h => new Date(h.date));
    
    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const lowestPrice = Math.min(...prices);
    const highestPrice = Math.max(...prices);
    
    // Calculate price changes
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const price24hAgo = this.findPriceAtDate(history, oneDayAgo);
    const price7dAgo = this.findPriceAtDate(history, sevenDaysAgo);
    const currentPrice = prices[prices.length - 1];
    
    const priceChange24h = price24hAgo ? ((currentPrice - price24hAgo) / price24hAgo) * 100 : 0;
    const priceChange7d = price7dAgo ? ((currentPrice - price7dAgo) / price7dAgo) * 100 : 0;
    
    // Determine trend
    const recentPrices = prices.slice(-5); // Last 5 data points
    const trend = this.calculateTrend(recentPrices);
    
    // Generate recommendation
    const recommendation = this.generateRecommendation(
      trend,
      currentPrice,
      averagePrice,
      lowestPrice,
      priceChange7d
    );

    return {
      trend,
      averagePrice: Math.round(averagePrice),
      lowestPrice,
      highestPrice,
      priceChange24h: Math.round(priceChange24h * 100) / 100,
      priceChange7d: Math.round(priceChange7d * 100) / 100,
      recommendation
    };
  }

  async predictPrices(searchKey: string, daysAhead: number = 30): Promise<{
    predictions: Array<{ date: string; predictedPrice: number; confidence: number }>;
    recommendation: string;
  }> {
    const history = await this.getPriceHistory(searchKey);
    
    if (history.length < 7) {
      return {
        predictions: [],
        recommendation: 'Not enough historical data for price prediction'
      };
    }

    // Simple linear regression for price prediction
    const predictions = this.generatePricePredictions(history, daysAhead);
    const recommendation = this.generatePredictionRecommendation(predictions, history);

    return { predictions, recommendation };
  }

  private async checkAlertPrice(alertId: string): Promise<void> {
    const alert = this.alerts.get(alertId);
    if (!alert || !alert.isActive) return;

    try {
      let currentPrice = 0;
      
      // This would integrate with your travel APIs to get current prices
      // For now, we'll simulate price checking
      currentPrice = await this.getCurrentPrice(alert.type, alert.criteria);
      
      const oldPrice = alert.currentPrice;
      alert.currentPrice = currentPrice;
      alert.lastChecked = new Date().toISOString();

      // Check if price dropped below target
      if (currentPrice <= alert.targetPrice && oldPrice > alert.targetPrice) {
        await this.createNotification(alert, 'price_drop', oldPrice, currentPrice);
      }
      
      // Check for significant price changes (>10%)
      if (oldPrice > 0) {
        const changePercent = Math.abs((currentPrice - oldPrice) / oldPrice) * 100;
        if (changePercent >= 10) {
          const type = currentPrice < oldPrice ? 'price_drop' : 'price_increase';
          await this.createNotification(alert, type, oldPrice, currentPrice);
        }
      }

      this.alerts.set(alertId, alert);
      await this.saveAlertsToStorage();
      
    } catch (error) {
      console.error(`Failed to check price for alert ${alertId}:`, error);
    }
  }

  private async getCurrentPrice(
    type: 'flight' | 'hotel',
    criteria: FlightSearchCriteria | HotelSearchCriteria
  ): Promise<number> {
    // This would integrate with your actual travel APIs
    // For now, return a simulated price
    const basePrice = type === 'flight' ? 500 : 150;
    const variation = (Math.random() - 0.5) * 0.2; // ±10% variation
    return Math.round(basePrice * (1 + variation));
  }

  private async createNotification(
    alert: PriceAlert,
    type: 'price_drop' | 'price_increase' | 'deal_found',
    oldPrice: number,
    newPrice: number
  ): Promise<void> {
    const savings = oldPrice - newPrice;
    const notification: PriceNotification = {
      id: this.generateNotificationId(),
      alertId: alert.id,
      type,
      oldPrice,
      newPrice,
      savings,
      message: this.generateNotificationMessage(type, savings, newPrice),
      timestamp: new Date().toISOString(),
      isRead: false
    };

    alert.notifications.push(notification);
    
    // Send browser notification if supported
    await this.sendBrowserNotification(notification);
  }

  private generateNotificationMessage(
    type: 'price_drop' | 'price_increase' | 'deal_found',
    savings: number,
    newPrice: number
  ): string {
    switch (type) {
      case 'price_drop':
        return `Great news! Price dropped by $${Math.abs(savings)}. New price: $${newPrice}`;
      case 'price_increase':
        return `Price increased by $${Math.abs(savings)}. Current price: $${newPrice}`;
      case 'deal_found':
        return `Amazing deal found! Save $${Math.abs(savings)} - only $${newPrice}`;
      default:
        return `Price update: $${newPrice}`;
    }
  }

  private async sendBrowserNotification(notification: PriceNotification): Promise<void> {
    if (typeof chrome !== 'undefined' && chrome.notifications) {
      try {
        await chrome.notifications.create({
          type: 'basic',
          iconUrl: 'logo_icon_mark-darkbg-ref.png',
          title: 'TravelSaver Price Alert',
          message: notification.message
        });
      } catch (error) {
        console.error('Failed to send browser notification:', error);
      }
    }
  }

  private startPriceMonitoring(): void {
    setInterval(async () => {
      const activeAlerts = Array.from(this.alerts.values()).filter(alert => alert.isActive);
      
      for (const alert of activeAlerts) {
        await this.checkAlertPrice(alert.id);
        // Add delay between checks to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }, this.checkInterval);
  }

  private findPriceAtDate(history: PriceHistory[], targetDate: Date): number | null {
    const closest = history.reduce((prev, curr) => {
      const prevDiff = Math.abs(new Date(prev.date).getTime() - targetDate.getTime());
      const currDiff = Math.abs(new Date(curr.date).getTime() - targetDate.getTime());
      return currDiff < prevDiff ? curr : prev;
    });
    
    return closest ? closest.price : null;
  }

  private calculateTrend(prices: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (prices.length < 2) return 'stable';
    
    let increases = 0;
    let decreases = 0;
    
    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > prices[i - 1]) increases++;
      else if (prices[i] < prices[i - 1]) decreases++;
    }
    
    if (increases > decreases) return 'increasing';
    if (decreases > increases) return 'decreasing';
    return 'stable';
  }

  private generateRecommendation(
    trend: 'increasing' | 'decreasing' | 'stable',
    currentPrice: number,
    averagePrice: number,
    lowestPrice: number,
    priceChange7d: number
  ): string {
    if (currentPrice <= lowestPrice * 1.05) {
      return 'Excellent price! This is near the lowest we\'ve seen. Book now!';
    }
    
    if (trend === 'decreasing' && priceChange7d < -5) {
      return 'Prices are dropping. Consider waiting a few more days.';
    }
    
    if (trend === 'increasing' && priceChange7d > 5) {
      return 'Prices are rising. Book soon to avoid higher costs.';
    }
    
    if (currentPrice < averagePrice * 0.9) {
      return 'Good deal! Price is below average. Consider booking.';
    }
    
    return 'Price is stable. Monitor for a few more days for better deals.';
  }

  private generatePricePredictions(
    history: PriceHistory[],
    daysAhead: number
  ): Array<{ date: string; predictedPrice: number; confidence: number }> {
    // Simple moving average prediction
    const predictions = [];
    const recentPrices = history.slice(-14).map(h => h.price); // Last 14 days
    const avgPrice = recentPrices.reduce((sum, price) => sum + price, 0) / recentPrices.length;
    
    for (let i = 1; i <= daysAhead; i++) {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + i);
      
      // Add some randomness to simulate market volatility
      const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
      const predictedPrice = Math.round(avgPrice * (1 + variation));
      const confidence = Math.max(0.3, 1 - (i / daysAhead) * 0.7); // Decreasing confidence over time
      
      predictions.push({
        date: futureDate.toISOString().split('T')[0],
        predictedPrice,
        confidence: Math.round(confidence * 100) / 100
      });
    }
    
    return predictions;
  }

  private generatePredictionRecommendation(
    predictions: Array<{ date: string; predictedPrice: number; confidence: number }>,
    history: PriceHistory[]
  ): string {
    if (predictions.length === 0) return 'No predictions available';
    
    const currentPrice = history[history.length - 1]?.price || 0;
    const avgPredictedPrice = predictions.reduce((sum, p) => sum + p.predictedPrice, 0) / predictions.length;
    
    if (avgPredictedPrice < currentPrice * 0.95) {
      return 'Prices are expected to drop. Consider waiting.';
    } else if (avgPredictedPrice > currentPrice * 1.05) {
      return 'Prices are expected to rise. Book now for better rates.';
    } else {
      return 'Prices are expected to remain stable.';
    }
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async saveAlertsToStorage(): Promise<void> {
    try {
      const alertsData = Array.from(this.alerts.entries());
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ priceAlerts: alertsData });
      } else {
        localStorage.setItem('priceAlerts', JSON.stringify(alertsData));
      }
    } catch (error) {
      console.error('Failed to save alerts to storage:', error);
    }
  }

  private async savePriceHistoryToStorage(): Promise<void> {
    try {
      const historyData = Array.from(this.priceHistory.entries());
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ priceHistory: historyData });
      } else {
        localStorage.setItem('priceHistory', JSON.stringify(historyData));
      }
    } catch (error) {
      console.error('Failed to save price history to storage:', error);
    }
  }

  private async loadStoredData(): Promise<void> {
    try {
      let alertsData, historyData;
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['priceAlerts', 'priceHistory']);
        alertsData = result.priceAlerts;
        historyData = result.priceHistory;
      } else {
        const alertsJson = localStorage.getItem('priceAlerts');
        const historyJson = localStorage.getItem('priceHistory');
        alertsData = alertsJson ? JSON.parse(alertsJson) : null;
        historyData = historyJson ? JSON.parse(historyJson) : null;
      }
      
      if (alertsData) {
        this.alerts = new Map(alertsData);
      }
      
      if (historyData) {
        this.priceHistory = new Map(historyData);
      }
    } catch (error) {
      console.error('Failed to load stored data:', error);
    }
  }
}
