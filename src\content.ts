// Content Script for TravelSaver Extension
// Runs on travel booking websites to enhance user experience

class TravelSaverContentScript {
  private isInjected = false;
  private observer: MutationObserver | null = null;
  private priceElements: Set<Element> = new Set();
  private overlayContainer: HTMLElement | null = null;

  constructor() {
    this.init();
  }

  private init() {
    if (this.isInjected) return;
    
    console.log('TravelSaver content script loaded on:', window.location.hostname);
    this.isInjected = true;
    
    // Wait for page to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupContentEnhancements());
    } else {
      this.setupContentEnhancements();
    }
  }

  private setupContentEnhancements() {
    const hostname = window.location.hostname;
    
    if (hostname.includes('booking.com')) {
      this.setupBookingComEnhancements();
    } else if (hostname.includes('expedia.com')) {
      this.setupExpediaEnhancements();
    } else if (hostname.includes('hotels.com')) {
      this.setupHotelsComEnhancements();
    } else {
      this.setupGenericEnhancements();
    }

    // Set up mutation observer to handle dynamic content
    this.setupMutationObserver();
    
    // Add TravelSaver overlay
    this.createOverlay();
  }

  private setupBookingComEnhancements() {
    console.log('Setting up Booking.com enhancements');
    
    // Find price elements
    const priceSelectors = [
      '[data-testid="price-and-discounted-price"]',
      '.bui-price-display__value',
      '.sr-hotel__price',
      '.bui-price-display'
    ];
    
    this.findAndEnhancePriceElements(priceSelectors);
    
    // Add comparison buttons to hotel cards
    this.addComparisonButtons('.sr_item, [data-testid="property-card"]');
    
    // Track page interactions
    this.trackBookingComInteractions();
  }

  private setupExpediaEnhancements() {
    console.log('Setting up Expedia enhancements');
    
    const priceSelectors = [
      '.price-current',
      '.price-total',
      '.uitk-text-emphasis-theme',
      '[data-stid="price-display"]'
    ];
    
    this.findAndEnhancePriceElements(priceSelectors);
    this.addComparisonButtons('[data-stid="lodging-card-responsive"]');
  }

  private setupHotelsComEnhancements() {
    console.log('Setting up Hotels.com enhancements');
    
    const priceSelectors = [
      '.current-price',
      '.price-current',
      '[data-stid="price-display"]'
    ];
    
    this.findAndEnhancePriceElements(priceSelectors);
    this.addComparisonButtons('[data-stid="property-listing"]');
  }

  private setupGenericEnhancements() {
    console.log('Setting up generic travel site enhancements');
    
    // Generic price selectors
    const priceSelectors = [
      '[class*="price"]',
      '[class*="cost"]',
      '[class*="rate"]',
      '[data-price]'
    ];
    
    this.findAndEnhancePriceElements(priceSelectors);
  }

  private findAndEnhancePriceElements(selectors: string[]) {
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (!this.priceElements.has(element)) {
          this.enhancePriceElement(element);
          this.priceElements.add(element);
        }
      });
    });
  }

  private enhancePriceElement(element: Element) {
    const priceText = element.textContent?.trim();
    if (!priceText) return;
    
    // Extract price value
    const priceMatch = priceText.match(/[\d,]+\.?\d*/);
    if (!priceMatch) return;
    
    const price = parseFloat(priceMatch[0].replace(/,/g, ''));
    if (isNaN(price)) return;
    
    // Add TravelSaver price comparison badge
    this.addPriceComparisonBadge(element, price);
  }

  private addPriceComparisonBadge(element: Element, price: number) {
    // Check if badge already exists
    if (element.querySelector('.travelsaver-badge')) return;
    
    const badge = document.createElement('div');
    badge.className = 'travelsaver-badge';
    badge.style.cssText = `
      position: relative;
      display: inline-block;
      background: linear-gradient(135deg, #FF385C, #E0314F);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: bold;
      margin-left: 4px;
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      z-index: 1000;
    `;
    
    // Simulate price comparison (in real implementation, this would call your APIs)
    const savings = Math.floor(Math.random() * 50) + 10;
    badge.textContent = `Save $${savings}`;
    badge.title = 'Click to see TravelSaver deals';
    
    badge.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.showPriceComparison(price, element);
    });
    
    // Insert badge after the price element
    element.parentNode?.insertBefore(badge, element.nextSibling);
  }

  private addComparisonButtons(containerSelector: string) {
    const containers = document.querySelectorAll(containerSelector);
    
    containers.forEach(container => {
      if (container.querySelector('.travelsaver-compare-btn')) return;
      
      const button = document.createElement('button');
      button.className = 'travelsaver-compare-btn';
      button.style.cssText = `
        position: absolute;
        top: 8px;
        right: 8px;
        background: #FF385C;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        cursor: pointer;
        z-index: 1000;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        transition: all 0.2s ease;
      `;
      
      button.textContent = 'Compare';
      button.title = 'Compare prices with TravelSaver';
      
      button.addEventListener('mouseenter', () => {
        button.style.background = '#E0314F';
        button.style.transform = 'scale(1.05)';
      });
      
      button.addEventListener('mouseleave', () => {
        button.style.background = '#FF385C';
        button.style.transform = 'scale(1)';
      });
      
      button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.openTravelSaverComparison(container);
      });
      
      // Make container relative if it's not already positioned
      const containerStyle = window.getComputedStyle(container);
      if (containerStyle.position === 'static') {
        (container as HTMLElement).style.position = 'relative';
      }
      
      container.appendChild(button);
    });
  }

  private createOverlay() {
    if (this.overlayContainer) return;
    
    this.overlayContainer = document.createElement('div');
    this.overlayContainer.id = 'travelsaver-overlay';
    this.overlayContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 10000;
      display: none;
      align-items: center;
      justify-content: center;
    `;
    
    document.body.appendChild(this.overlayContainer);
  }

  private showPriceComparison(price: number, element: Element) {
    if (!this.overlayContainer) return;
    
    // Create comparison modal
    const modal = document.createElement('div');
    modal.style.cssText = `
      background: white;
      border-radius: 12px;
      padding: 24px;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      position: relative;
    `;
    
    modal.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2 style="margin: 0; color: #FF385C; font-size: 24px;">TravelSaver Price Comparison</h2>
        <button id="close-modal" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
      </div>
      
      <div style="margin-bottom: 20px;">
        <p style="margin: 0 0 10px 0; font-size: 16px;">Current Price: <strong>$${price}</strong></p>
        <div style="background: #f8f9fa; padding: 16px; border-radius: 8px;">
          <h3 style="margin: 0 0 12px 0; color: #28a745;">🎉 Better Deals Found!</h3>
          <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Booking.com (via TravelSaver)</span>
            <strong style="color: #28a745;">$${price - 25}</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Expedia (via TravelSaver)</span>
            <strong style="color: #28a745;">$${price - 18}</strong>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <span>Hotels.com (via TravelSaver)</span>
            <strong style="color: #28a745;">$${price - 32}</strong>
          </div>
        </div>
      </div>
      
      <div style="display: flex; gap: 12px;">
        <button id="view-deals" style="
          flex: 1;
          background: #FF385C;
          color: white;
          border: none;
          padding: 12px;
          border-radius: 6px;
          font-weight: bold;
          cursor: pointer;
        ">View All Deals</button>
        <button id="set-alert" style="
          flex: 1;
          background: #007bff;
          color: white;
          border: none;
          padding: 12px;
          border-radius: 6px;
          font-weight: bold;
          cursor: pointer;
        ">Set Price Alert</button>
      </div>
    `;
    
    // Clear previous content and add modal
    this.overlayContainer.innerHTML = '';
    this.overlayContainer.appendChild(modal);
    this.overlayContainer.style.display = 'flex';
    
    // Add event listeners
    modal.querySelector('#close-modal')?.addEventListener('click', () => {
      this.overlayContainer!.style.display = 'none';
    });
    
    modal.querySelector('#view-deals')?.addEventListener('click', () => {
      this.openTravelSaverExtension();
    });
    
    modal.querySelector('#set-alert')?.addEventListener('click', () => {
      this.createPriceAlert(price);
    });
    
    // Close on overlay click
    this.overlayContainer.addEventListener('click', (e) => {
      if (e.target === this.overlayContainer) {
        this.overlayContainer!.style.display = 'none';
      }
    });
  }

  private openTravelSaverComparison(container: Element) {
    // Extract hotel/flight information from the container
    const titleElement = container.querySelector('h3, h4, .hotel-name, [data-testid="title"]');
    const title = titleElement?.textContent?.trim() || 'Travel Deal';
    
    console.log('Opening TravelSaver comparison for:', title);
    
    // Send message to extension
    chrome.runtime.sendMessage({
      type: 'OPEN_COMPARISON',
      data: {
        title,
        url: window.location.href,
        timestamp: new Date().toISOString()
      }
    });
    
    this.openTravelSaverExtension();
  }

  private openTravelSaverExtension() {
    // This will open the extension popup
    chrome.runtime.sendMessage({
      type: 'OPEN_EXTENSION',
      data: {
        source: 'content_script',
        url: window.location.href
      }
    });
  }

  private createPriceAlert(price: number) {
    const targetPrice = price * 0.9; // 10% discount target
    
    chrome.runtime.sendMessage({
      type: 'CREATE_PRICE_ALERT',
      data: {
        type: 'hotel', // Determine based on site
        targetPrice,
        criteria: {
          destination: this.extractDestination(),
          url: window.location.href
        }
      }
    }, (response) => {
      if (response?.success) {
        this.showNotification('Price alert created! We\'ll notify you when prices drop.');
      }
    });
    
    this.overlayContainer!.style.display = 'none';
  }

  private extractDestination(): string {
    // Try to extract destination from page
    const selectors = [
      '[data-testid="destination"]',
      '.destination',
      'h1',
      'title'
    ];
    
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent) {
        return element.textContent.trim();
      }
    }
    
    return window.location.hostname;
  }

  private showNotification(message: string) {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      z-index: 10001;
      font-weight: bold;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      animation: slideIn 0.3s ease;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 4000);
  }

  private trackBookingComInteractions() {
    // Track clicks on booking buttons
    document.addEventListener('click', (e) => {
      const target = e.target as Element;
      
      if (target.closest('[data-testid="availability-cta"]') || 
          target.closest('.bui-button--primary')) {
        
        chrome.runtime.sendMessage({
          type: 'TRACK_AFFILIATE_CLICK',
          data: {
            partner: 'booking.com',
            offerType: 'hotel',
            offerId: this.extractOfferId(),
            timestamp: new Date().toISOString()
          }
        });
      }
    });
  }

  private extractOfferId(): string {
    // Extract hotel ID or offer ID from URL or page
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('hotel_id') || 
           urlParams.get('ss') || 
           window.location.pathname.split('/').pop() || 
           'unknown';
  }

  private setupMutationObserver() {
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              
              // Re-scan for new price elements
              const priceSelectors = [
                '[class*="price"]',
                '[data-testid*="price"]',
                '.bui-price-display'
              ];
              
              priceSelectors.forEach(selector => {
                const newPriceElements = element.querySelectorAll(selector);
                newPriceElements.forEach(priceElement => {
                  if (!this.priceElements.has(priceElement)) {
                    this.enhancePriceElement(priceElement);
                    this.priceElements.add(priceElement);
                  }
                });
              });
            }
          });
        }
      });
    });
    
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// Initialize content script
new TravelSaverContentScript();

export {};
