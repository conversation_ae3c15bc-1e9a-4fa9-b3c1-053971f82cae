import { ApiClient, API_ENDPOINTS, rateLimiter } from './api-client';
import { UserLocation } from '../types/travel';

export class GeolocationService {
  private client: ApiClient;

  constructor() {
    this.client = new ApiClient('', '');
  }

  async getCurrentLocation(): Promise<UserLocation> {
    try {
      // Try browser geolocation first
      const browserLocation = await this.getBrowserLocation();
      if (browserLocation) {
        return await this.enrichLocationData(browserLocation);
      }
    } catch (error) {
      console.warn('Browser geolocation failed:', error);
    }

    // Fallback to IP-based geolocation
    return await this.getIPLocation();
  }

  private async getBrowserLocation(): Promise<UserLocation | null> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  private async getIPLocation(): Promise<UserLocation> {
    if (!(await rateLimiter.checkLimit('geolocation'))) {
      throw new Error('Rate limit exceeded for geolocation service');
    }

    try {
      const response = await fetch(API_ENDPOINTS.GEOLOCATION.IPAPI);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(`IP geolocation failed: ${data.reason || 'Unknown error'}`);
      }

      return {
        latitude: data.latitude,
        longitude: data.longitude,
        city: data.city,
        country: data.country_name,
        countryCode: data.country_code,
        timezone: data.timezone
      };
    } catch (error) {
      console.error('IP geolocation failed:', error);
      throw new Error('Failed to get location from IP');
    }
  }

  private async enrichLocationData(location: UserLocation): Promise<UserLocation> {
    if (location.city && location.country) {
      return location; // Already enriched
    }

    try {
      if (!(await rateLimiter.checkLimit('geolocation'))) {
        return location; // Return basic location if rate limited
      }

      // Use reverse geocoding to get city/country info
      const response = await fetch(
        `${API_ENDPOINTS.GEOLOCATION.OPENWEATHER_GEO}/reverse?lat=${location.latitude}&lon=${location.longitude}&limit=1&appid=${this.getOpenWeatherApiKey()}`
      );

      if (response.ok) {
        const data = await response.json();
        if (data.length > 0) {
          const place = data[0];
          return {
            ...location,
            city: place.name,
            country: place.country,
            countryCode: place.country
          };
        }
      }
    } catch (error) {
      console.warn('Failed to enrich location data:', error);
    }

    return location;
  }

  async getNearbyAirports(location: UserLocation, radiusKm: number = 100): Promise<Array<{
    code: string;
    name: string;
    city: string;
    distance: number;
  }>> {
    // This would typically use a dedicated airport database or API
    // For now, return mock data based on major airports
    const majorAirports = [
      { code: 'JFK', name: 'John F. Kennedy International', city: 'New York', lat: 40.6413, lon: -73.7781 },
      { code: 'LAX', name: 'Los Angeles International', city: 'Los Angeles', lat: 33.9425, lon: -118.4081 },
      { code: 'LHR', name: 'London Heathrow', city: 'London', lat: 51.4700, lon: -0.4543 },
      { code: 'CDG', name: 'Charles de Gaulle', city: 'Paris', lat: 49.0097, lon: 2.5479 },
      { code: 'NRT', name: 'Narita International', city: 'Tokyo', lat: 35.7720, lon: 140.3929 },
      { code: 'SYD', name: 'Sydney Kingsford Smith', city: 'Sydney', lat: -33.9399, lon: 151.1753 },
      { code: 'DXB', name: 'Dubai International', city: 'Dubai', lat: 25.2532, lon: 55.3657 },
      { code: 'SIN', name: 'Singapore Changi', city: 'Singapore', lat: 1.3644, lon: 103.9915 }
    ];

    const nearbyAirports = majorAirports
      .map(airport => ({
        ...airport,
        distance: this.calculateDistance(
          location.latitude,
          location.longitude,
          airport.lat,
          airport.lon
        )
      }))
      .filter(airport => airport.distance <= radiusKm)
      .sort((a, b) => a.distance - b.distance)
      .map(airport => ({
        code: airport.code,
        name: airport.name,
        city: airport.city,
        distance: Math.round(airport.distance)
      }));

    return nearbyAirports;
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private getOpenWeatherApiKey(): string {
    // In a real implementation, this would come from environment variables
    return process.env.OPENWEATHER_API_KEY || 'your-openweather-api-key';
  }

  async getLocationFromQuery(query: string): Promise<UserLocation[]> {
    if (!(await rateLimiter.checkLimit('geolocation'))) {
      throw new Error('Rate limit exceeded for geolocation service');
    }

    try {
      const response = await fetch(
        `${API_ENDPOINTS.GEOLOCATION.OPENWEATHER_GEO}/direct?q=${encodeURIComponent(query)}&limit=5&appid=${this.getOpenWeatherApiKey()}`
      );

      if (!response.ok) {
        throw new Error('Location search failed');
      }

      const data = await response.json();
      
      return data.map((place: any) => ({
        latitude: place.lat,
        longitude: place.lon,
        city: place.name,
        country: place.country,
        countryCode: place.country
      }));
    } catch (error) {
      console.error('Location search failed:', error);
      throw new Error('Failed to search locations');
    }
  }

  // Store location in Chrome storage for persistence
  async saveUserLocation(location: UserLocation): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ userLocation: location });
      } else {
        // Fallback to localStorage for development
        localStorage.setItem('userLocation', JSON.stringify(location));
      }
    } catch (error) {
      console.error('Failed to save user location:', error);
    }
  }

  async getSavedUserLocation(): Promise<UserLocation | null> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['userLocation']);
        return result.userLocation || null;
      } else {
        // Fallback to localStorage for development
        const saved = localStorage.getItem('userLocation');
        return saved ? JSON.parse(saved) : null;
      }
    } catch (error) {
      console.error('Failed to get saved user location:', error);
      return null;
    }
  }
}
