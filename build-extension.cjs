#!/usr/bin/env node

/**
 * Build script for TravelSaver Chrome Extension
 * This script builds the extension without requiring complex dependencies
 */

const fs = require('fs');
const path = require('path');

// Create dist-extension directory
const distDir = path.join(__dirname, 'dist-extension');

if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Copy manifest.json
const manifestSource = path.join(__dirname, 'manifest.json');
const manifestDest = path.join(distDir, 'manifest.json');

if (fs.existsSync(manifestSource)) {
  fs.copyFileSync(manifestSource, manifestDest);
  console.log('✓ Copied manifest.json');
} else {
  console.error('✗ manifest.json not found');
}

// Copy public assets
const publicDir = path.join(__dirname, 'public');
const publicFiles = ['logo_icon_mark-darkbg-ref.png'];

publicFiles.forEach(file => {
  const source = path.join(publicDir, file);
  const dest = path.join(distDir, file);
  
  if (fs.existsSync(source)) {
    fs.copyFileSync(source, dest);
    console.log(`✓ Copied ${file}`);
  } else {
    console.warn(`⚠ ${file} not found in public directory`);
  }
});

// Copy index.html
const indexSource = path.join(__dirname, 'index.html');
const indexDest = path.join(distDir, 'index.html');

if (fs.existsSync(indexSource)) {
  fs.copyFileSync(indexSource, indexDest);
  console.log('✓ Copied index.html');
}

// Create simple background.js (placeholder)
const backgroundJs = `
// TravelSaver Background Script
console.log('TravelSaver background script loaded');

// Basic extension functionality
chrome.runtime.onInstalled.addListener(() => {
  console.log('TravelSaver extension installed');
});

chrome.action.onClicked.addListener((tab) => {
  console.log('TravelSaver icon clicked');
});
`;

fs.writeFileSync(path.join(distDir, 'background.js'), backgroundJs);
console.log('✓ Created background.js');

// Create simple content.js (placeholder)
const contentJs = `
// TravelSaver Content Script
console.log('TravelSaver content script loaded on:', window.location.hostname);

// Basic price enhancement functionality
function enhancePrices() {
  const priceElements = document.querySelectorAll('[class*="price"], [data-testid*="price"]');
  
  priceElements.forEach(element => {
    if (!element.querySelector('.travelsaver-badge')) {
      const badge = document.createElement('span');
      badge.className = 'travelsaver-badge';
      badge.textContent = 'Save $25';
      badge.style.cssText = \`
        background: linear-gradient(135deg, #FF385C, #E0314F);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: bold;
        margin-left: 4px;
        cursor: pointer;
      \`;
      
      element.appendChild(badge);
    }
  });
}

// Run enhancement
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', enhancePrices);
} else {
  enhancePrices();
}

// Re-run on dynamic content changes
const observer = new MutationObserver(enhancePrices);
observer.observe(document.body, { childList: true, subtree: true });
`;

fs.writeFileSync(path.join(distDir, 'content.js'), contentJs);
console.log('✓ Created content.js');

// Copy content.css
const contentCssSource = path.join(__dirname, 'src', 'content.css');
const contentCssDest = path.join(distDir, 'content.css');

if (fs.existsSync(contentCssSource)) {
  fs.copyFileSync(contentCssSource, contentCssDest);
  console.log('✓ Copied content.css');
}

// Create a simple popup HTML that doesn't require React build
const popupHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TravelSaver</title>
  <style>
    body {
      width: 400px;
      height: 600px;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
    }
    
    .header {
      background: linear-gradient(135deg, #FF385C, #E0314F);
      color: white;
      padding: 16px;
      text-align: center;
    }
    
    .logo {
      width: 32px;
      height: 32px;
      margin-bottom: 8px;
    }
    
    .title {
      font-size: 18px;
      font-weight: bold;
      margin: 0;
    }
    
    .content {
      padding: 20px;
    }
    
    .feature {
      background: white;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .feature h3 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 14px;
    }
    
    .feature p {
      margin: 0;
      color: #666;
      font-size: 12px;
    }
    
    .cta-button {
      background: #FF385C;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      font-weight: bold;
      cursor: pointer;
      width: 100%;
      margin-top: 16px;
    }
    
    .cta-button:hover {
      background: #E0314F;
    }
    
    .status {
      text-align: center;
      padding: 16px;
      background: #e8f5e8;
      color: #2d5a2d;
      border-radius: 6px;
      margin-bottom: 16px;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="header">
    <img src="logo_icon_mark-darkbg-ref.png" alt="TravelSaver" class="logo">
    <h1 class="title">TravelSaver</h1>
  </div>
  
  <div class="content">
    <div class="status">
      ✅ Extension Active - Finding deals on this page
    </div>
    
    <div class="feature">
      <h3>🔍 Smart Price Tracking</h3>
      <p>Monitor flight and hotel prices across multiple providers with real-time alerts.</p>
    </div>
    
    <div class="feature">
      <h3>💰 Affiliate Integration</h3>
      <p>Earn commissions through Booking.com, Expedia, and other travel partners.</p>
    </div>
    
    <div class="feature">
      <h3>📊 Price Analytics</h3>
      <p>View historical trends and future price predictions to book at the best time.</p>
    </div>
    
    <div class="feature">
      <h3>🌍 Global Deals</h3>
      <p>Discover the cheapest flights and hotels worldwide with location-based recommendations.</p>
    </div>
    
    <button class="cta-button" onclick="openFullApp()">
      Open Full TravelSaver App
    </button>
  </div>
  
  <script>
    function openFullApp() {
      chrome.tabs.create({
        url: chrome.runtime.getURL('index.html')
      });
    }
    
    // Check if we're on a travel site
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      const url = tabs[0].url;
      const travelSites = ['booking.com', 'expedia.com', 'hotels.com', 'kayak.com'];
      const isOnTravelSite = travelSites.some(site => url.includes(site));
      
      if (isOnTravelSite) {
        document.querySelector('.status').innerHTML = 
          '🎯 Travel site detected - Enhanced features active';
      }
    });
  </script>
</body>
</html>
`;

fs.writeFileSync(path.join(distDir, 'popup.html'), popupHtml);
console.log('✓ Created popup.html');

// Update manifest to use popup.html instead of index.html
const manifest = JSON.parse(fs.readFileSync(manifestDest, 'utf8'));
manifest.action.default_popup = 'popup.html';
fs.writeFileSync(manifestDest, JSON.stringify(manifest, null, 2));
console.log('✓ Updated manifest.json for popup');

console.log('\n🎉 Extension built successfully!');
console.log('\nTo install:');
console.log('1. Open Chrome and go to chrome://extensions/');
console.log('2. Enable "Developer mode"');
console.log('3. Click "Load unpacked" and select the dist-extension folder');
console.log('\nThe extension will be ready to use!');
