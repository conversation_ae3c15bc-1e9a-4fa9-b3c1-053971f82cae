# TravelSaver Extension Environment Variables

# Amadeus API Credentials
# Get these from: https://developers.amadeus.com/
AMADEUS_CLIENT_ID=your_amadeus_client_id_here
AMADEUS_CLIENT_SECRET=your_amadeus_client_secret_here

# Skyscanner API Credentials (Optional)
# Get these from: https://partners.skyscanner.net/
SKYSCANNER_API_KEY=your_skyscanner_api_key_here

# OpenWeather API Key (for geolocation services)
# Get this from: https://openweathermap.org/api
OPENWEATHER_API_KEY=your_openweather_api_key_here

# Affiliate Program IDs
# Booking.com Affiliate ID
BOOKING_AFFILIATE_ID=your_booking_affiliate_id_here

# Expedia Affiliate ID
EXPEDIA_AFFILIATE_ID=your_expedia_affiliate_id_here

# Hotels.com Affiliate ID
HOTELS_AFFILIATE_ID=your_hotels_affiliate_id_here

# Extension Configuration
EXTENSION_VERSION=1.0.0
EXTENSION_NAME=TravelSaver

# Development Settings
NODE_ENV=development
VITE_APP_TITLE=TravelSaver - Smart Travel Deals
