import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

export class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;
  private apiKey: string;

  constructor(baseURL: string, apiKey: string, additionalHeaders: Record<string, string> = {}) {
    this.baseURL = baseURL;
    this.apiKey = apiKey;
    
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        ...additionalHeaders
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        
        if (error.response?.status === 401) {
          console.error('API Authentication failed');
        } else if (error.response?.status === 429) {
          console.error('API Rate limit exceeded');
        }
        
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.patch(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  updateApiKey(newApiKey: string) {
    this.apiKey = newApiKey;
    this.client.defaults.headers['Authorization'] = `Bearer ${newApiKey}`;
  }

  updateBaseURL(newBaseURL: string) {
    this.baseURL = newBaseURL;
    this.client.defaults.baseURL = newBaseURL;
  }
}

// API Configuration
export const API_ENDPOINTS = {
  AMADEUS: {
    BASE_URL: 'https://api.amadeus.com',
    AUTH: '/v1/security/oauth2/token',
    FLIGHT_OFFERS: '/v2/shopping/flight-offers',
    FLIGHT_PRICES: '/v1/shopping/flight-offers/pricing',
    HOTEL_OFFERS: '/v3/shopping/hotel-offers',
    LOCATIONS: '/v1/reference-data/locations',
    AIRLINES: '/v1/reference-data/airlines'
  },
  SKYSCANNER: {
    BASE_URL: 'https://partners.api.skyscanner.net',
    FLIGHT_SEARCH: '/apiservices/v3/flights/live/search/create',
    FLIGHT_RESULTS: '/apiservices/v3/flights/live/search/poll',
    HOTELS: '/apiservices/hotels/liveprices/v3'
  },
  GEOLOCATION: {
    IPAPI: 'https://ipapi.co/json/',
    OPENWEATHER_GEO: 'https://api.openweathermap.org/geo/1.0'
  }
};

// Rate limiting helper
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private limits: Map<string, { requests: number; window: number }> = new Map();

  constructor() {
    // Set default rate limits (requests per minute)
    this.limits.set('amadeus', { requests: 10, window: 60000 });
    this.limits.set('skyscanner', { requests: 100, window: 60000 });
    this.limits.set('geolocation', { requests: 1000, window: 60000 });
  }

  async checkLimit(service: string): Promise<boolean> {
    const now = Date.now();
    const limit = this.limits.get(service);
    
    if (!limit) return true;

    const serviceRequests = this.requests.get(service) || [];
    
    // Remove old requests outside the window
    const validRequests = serviceRequests.filter(
      timestamp => now - timestamp < limit.window
    );

    if (validRequests.length >= limit.requests) {
      const oldestRequest = Math.min(...validRequests);
      const waitTime = limit.window - (now - oldestRequest);
      
      console.warn(`Rate limit reached for ${service}. Wait ${waitTime}ms`);
      return false;
    }

    // Add current request
    validRequests.push(now);
    this.requests.set(service, validRequests);
    
    return true;
  }

  setLimit(service: string, requests: number, windowMs: number) {
    this.limits.set(service, { requests, window: windowMs });
  }
}

export const rateLimiter = new RateLimiter();
