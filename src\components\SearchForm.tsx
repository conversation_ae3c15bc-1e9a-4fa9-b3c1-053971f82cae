import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, MapPin, Calendar, Users, Plane, Hotel } from 'lucide-react';
import { useTravelStore, useTravelActions, useUserLocation } from '../store/travel-store';
import { FlightSearchCriteria, HotelSearchCriteria } from '../types/travel';

interface SearchFormProps {
  type: 'flights' | 'hotels';
}

export function SearchForm({ type }: SearchFormProps) {
  const userLocation = useUserLocation();
  const { searchFlights, searchHotels, searchDestinations } = useTravelActions();
  
  // Flight search state
  const [flightCriteria, setFlightCriteria] = useState<FlightSearchCriteria>({
    origin: '',
    destination: '',
    departureDate: '',
    returnDate: '',
    adults: 1,
    children: 0,
    infants: 0,
    travelClass: 'ECONOMY',
    nonStop: false
  });

  // Hotel search state
  const [hotelCriteria, setHotelCriteria] = useState<HotelSearchCriteria>({
    destination: '',
    checkIn: '',
    checkOut: '',
    adults: 2,
    children: 0,
    rooms: 1
  });

  const [destinationSuggestions, setDestinationSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // Set default dates
  useEffect(() => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    if (type === 'flights') {
      setFlightCriteria(prev => ({
        ...prev,
        departureDate: nextWeek.toISOString().split('T')[0],
        returnDate: new Date(nextWeek.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }));
    } else {
      setHotelCriteria(prev => ({
        ...prev,
        checkIn: nextWeek.toISOString().split('T')[0],
        checkOut: new Date(nextWeek.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }));
    }
  }, [type]);

  // Auto-fill origin based on user location
  useEffect(() => {
    if (userLocation && type === 'flights' && !flightCriteria.origin) {
      // This would typically use a service to find nearest airport
      const nearestAirport = userLocation.city?.substring(0, 3).toUpperCase() || 'NYC';
      setFlightCriteria(prev => ({ ...prev, origin: nearestAirport }));
    }
  }, [userLocation, type, flightCriteria.origin]);

  const handleDestinationSearch = async (query: string, field: 'origin' | 'destination') => {
    if (query.length < 2) {
      setDestinationSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      await searchDestinations(query);
      // In a real implementation, you'd get suggestions from the store
      const mockSuggestions = [
        { code: 'NYC', name: 'New York', city: 'New York', country: 'United States' },
        { code: 'LAX', name: 'Los Angeles', city: 'Los Angeles', country: 'United States' },
        { code: 'LHR', name: 'London Heathrow', city: 'London', country: 'United Kingdom' },
        { code: 'CDG', name: 'Paris Charles de Gaulle', city: 'Paris', country: 'France' },
        { code: 'NRT', name: 'Tokyo Narita', city: 'Tokyo', country: 'Japan' }
      ].filter(item => 
        item.name.toLowerCase().includes(query.toLowerCase()) ||
        item.city.toLowerCase().includes(query.toLowerCase()) ||
        item.code.toLowerCase().includes(query.toLowerCase())
      );
      
      setDestinationSuggestions(mockSuggestions);
      setShowSuggestions(true);
    } catch (error) {
      console.error('Destination search failed:', error);
    }
  };

  const selectDestination = (suggestion: any, field: 'origin' | 'destination') => {
    if (type === 'flights') {
      setFlightCriteria(prev => ({
        ...prev,
        [field]: suggestion.code
      }));
    } else {
      setHotelCriteria(prev => ({
        ...prev,
        destination: suggestion.city
      }));
    }
    setShowSuggestions(false);
    setDestinationSuggestions([]);
  };

  const handleSearch = async () => {
    setIsSearching(true);
    
    try {
      if (type === 'flights') {
        if (!flightCriteria.origin || !flightCriteria.destination || !flightCriteria.departureDate) {
          alert('Please fill in all required fields');
          return;
        }
        await searchFlights(flightCriteria);
      } else {
        if (!hotelCriteria.destination || !hotelCriteria.checkIn || !hotelCriteria.checkOut) {
          alert('Please fill in all required fields');
          return;
        }
        await searchHotels(hotelCriteria);
      }
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };

  if (type === 'flights') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-lg p-6 mb-6"
      >
        <div className="flex items-center gap-2 mb-4">
          <Plane className="text-[#FF385C]" size={20} />
          <h3 className="text-lg font-semibold">Search Flights</h3>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          {/* Origin */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">From</label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 text-gray-400" size={16} />
              <input
                type="text"
                value={flightCriteria.origin}
                onChange={(e) => {
                  setFlightCriteria(prev => ({ ...prev, origin: e.target.value }));
                  handleDestinationSearch(e.target.value, 'origin');
                }}
                placeholder="Origin airport"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
              />
            </div>
          </div>

          {/* Destination */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">To</label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 text-gray-400" size={16} />
              <input
                type="text"
                value={flightCriteria.destination}
                onChange={(e) => {
                  setFlightCriteria(prev => ({ ...prev, destination: e.target.value }));
                  handleDestinationSearch(e.target.value, 'destination');
                }}
                placeholder="Destination airport"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          {/* Departure Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Departure</label>
            <div className="relative">
              <Calendar className="absolute left-3 top-3 text-gray-400" size={16} />
              <input
                type="date"
                value={flightCriteria.departureDate}
                onChange={(e) => setFlightCriteria(prev => ({ ...prev, departureDate: e.target.value }))}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
              />
            </div>
          </div>

          {/* Return Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Return</label>
            <div className="relative">
              <Calendar className="absolute left-3 top-3 text-gray-400" size={16} />
              <input
                type="date"
                value={flightCriteria.returnDate}
                onChange={(e) => setFlightCriteria(prev => ({ ...prev, returnDate: e.target.value }))}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4 mb-4">
          {/* Adults */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Adults</label>
            <div className="relative">
              <Users className="absolute left-3 top-3 text-gray-400" size={16} />
              <select
                value={flightCriteria.adults}
                onChange={(e) => setFlightCriteria(prev => ({ ...prev, adults: parseInt(e.target.value) }))}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
              >
                {[1, 2, 3, 4, 5, 6].map(num => (
                  <option key={num} value={num}>{num}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Children */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Children</label>
            <select
              value={flightCriteria.children}
              onChange={(e) => setFlightCriteria(prev => ({ ...prev, children: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
            >
              {[0, 1, 2, 3, 4].map(num => (
                <option key={num} value={num}>{num}</option>
              ))}
            </select>
          </div>

          {/* Class */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Class</label>
            <select
              value={flightCriteria.travelClass}
              onChange={(e) => setFlightCriteria(prev => ({ ...prev, travelClass: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
            >
              <option value="ECONOMY">Economy</option>
              <option value="PREMIUM_ECONOMY">Premium Economy</option>
              <option value="BUSINESS">Business</option>
              <option value="FIRST">First</option>
            </select>
          </div>
        </div>

        {/* Suggestions Dropdown */}
        {showSuggestions && destinationSuggestions.length > 0 && (
          <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
            {destinationSuggestions.map((suggestion, index) => (
              <div
                key={index}
                onClick={() => selectDestination(suggestion, 'destination')}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              >
                <div className="font-medium">{suggestion.code} - {suggestion.name}</div>
                <div className="text-sm text-gray-600">{suggestion.city}, {suggestion.country}</div>
              </div>
            ))}
          </div>
        )}

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleSearch}
          disabled={isSearching}
          className="w-full bg-[#FF385C] text-white py-3 rounded-md font-semibold hover:bg-[#E0314F] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          <Search size={20} />
          {isSearching ? 'Searching...' : 'Search Flights'}
        </motion.button>
      </motion.div>
    );
  }

  // Hotel search form (similar structure)
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-lg p-6 mb-6"
    >
      <div className="flex items-center gap-2 mb-4">
        <Hotel className="text-[#FF385C]" size={20} />
        <h3 className="text-lg font-semibold">Search Hotels</h3>
      </div>

      {/* Destination */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Destination</label>
        <div className="relative">
          <MapPin className="absolute left-3 top-3 text-gray-400" size={16} />
          <input
            type="text"
            value={hotelCriteria.destination}
            onChange={(e) => {
              setHotelCriteria(prev => ({ ...prev, destination: e.target.value }));
              handleDestinationSearch(e.target.value, 'destination');
            }}
            placeholder="City or hotel name"
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        {/* Check-in */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Check-in</label>
          <div className="relative">
            <Calendar className="absolute left-3 top-3 text-gray-400" size={16} />
            <input
              type="date"
              value={hotelCriteria.checkIn}
              onChange={(e) => setHotelCriteria(prev => ({ ...prev, checkIn: e.target.value }))}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
            />
          </div>
        </div>

        {/* Check-out */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Check-out</label>
          <div className="relative">
            <Calendar className="absolute left-3 top-3 text-gray-400" size={16} />
            <input
              type="date"
              value={hotelCriteria.checkOut}
              onChange={(e) => setHotelCriteria(prev => ({ ...prev, checkOut: e.target.value }))}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-4">
        {/* Adults */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Adults</label>
          <div className="relative">
            <Users className="absolute left-3 top-3 text-gray-400" size={16} />
            <select
              value={hotelCriteria.adults}
              onChange={(e) => setHotelCriteria(prev => ({ ...prev, adults: parseInt(e.target.value) }))}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
            >
              {[1, 2, 3, 4, 5, 6].map(num => (
                <option key={num} value={num}>{num}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Children */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Children</label>
          <select
            value={hotelCriteria.children}
            onChange={(e) => setHotelCriteria(prev => ({ ...prev, children: parseInt(e.target.value) }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
          >
            {[0, 1, 2, 3, 4].map(num => (
              <option key={num} value={num}>{num}</option>
            ))}
          </select>
        </div>

        {/* Rooms */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Rooms</label>
          <select
            value={hotelCriteria.rooms}
            onChange={(e) => setHotelCriteria(prev => ({ ...prev, rooms: parseInt(e.target.value) }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
          >
            {[1, 2, 3, 4].map(num => (
              <option key={num} value={num}>{num}</option>
            ))}
          </select>
        </div>
      </div>

      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={handleSearch}
        disabled={isSearching}
        className="w-full bg-[#FF385C] text-white py-3 rounded-md font-semibold hover:bg-[#E0314F] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
      >
        <Search size={20} />
        {isSearching ? 'Searching...' : 'Search Hotels'}
      </motion.button>
    </motion.div>
  );
}
