import { create } from 'zustand';
import { TravelService } from '../services/travel-service';
import { 
  FlightOffer, 
  HotelOffer, 
  Deal, 
  UserLocation, 
  PriceAlert,
  FlightSearchCriteria,
  HotelSearchCriteria,
  Location
} from '../types/travel';

interface TravelState {
  // Services
  travelService: TravelService;
  
  // User data
  userLocation: UserLocation | null;
  
  // Search data
  flightOffers: FlightOffer[];
  hotelOffers: HotelOffer[];
  searchLocations: Location[];
  
  // Deals and alerts
  localDeals: Deal[];
  priceAlerts: PriceAlert[];
  
  // UI state
  isLoading: boolean;
  isSearching: boolean;
  error: string | null;
  activeTab: 'flights' | 'hotels';
  
  // Search criteria
  flightSearchCriteria: FlightSearchCriteria | null;
  hotelSearchCriteria: HotelSearchCriteria | null;
  
  // Price tracking
  priceHistory: Record<string, any>;
  priceTrends: Record<string, any>;
  
  // Actions
  initializeServices: () => Promise<void>;
  setActiveTab: (tab: 'flights' | 'hotels') => void;
  setUserLocation: (location: UserLocation) => void;
  
  // Search actions
  searchFlights: (criteria: FlightSearchCriteria) => Promise<void>;
  searchHotels: (criteria: HotelSearchCriteria) => Promise<void>;
  searchDestinations: (query: string) => Promise<void>;
  
  // Deal actions
  loadLocalDeals: () => Promise<void>;
  refreshDeals: () => Promise<void>;
  
  // Price tracking actions
  createPriceAlert: (type: 'flight' | 'hotel', criteria: any, targetPrice: number) => Promise<string>;
  loadPriceAlerts: () => Promise<void>;
  deletePriceAlert: (alertId: string) => Promise<void>;
  
  // Price analysis
  analyzePriceTrends: (searchKey: string) => Promise<void>;
  getPricePredictions: (searchKey: string, daysAhead?: number) => Promise<any>;
  
  // Affiliate tracking
  trackAffiliateClick: (partner: string, offerType: 'hotel' | 'flight', offerId: string) => Promise<void>;
  
  // Utility actions
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useTravelStore = create<TravelState>((set, get) => ({
  // Initial state
  travelService: new TravelService(),
  userLocation: null,
  flightOffers: [],
  hotelOffers: [],
  searchLocations: [],
  localDeals: [],
  priceAlerts: [],
  isLoading: false,
  isSearching: false,
  error: null,
  activeTab: 'flights',
  flightSearchCriteria: null,
  hotelSearchCriteria: null,
  priceHistory: {},
  priceTrends: {},

  // Actions
  initializeServices: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const { travelService } = get();
      
      // Initialize travel service with API keys from environment or storage
      const amadeusClientId = process.env.AMADEUS_CLIENT_ID;
      const amadeusClientSecret = process.env.AMADEUS_CLIENT_SECRET;
      
      if (amadeusClientId && amadeusClientSecret) {
        await travelService.initialize(amadeusClientId, amadeusClientSecret);
      } else {
        console.warn('Amadeus API credentials not found. Some features may be limited.');
      }
      
      // Get user location
      try {
        const location = await travelService.getUserLocation();
        set({ userLocation: location });
      } catch (error) {
        console.warn('Failed to get user location:', error);
      }
      
      // Load initial data
      await get().loadLocalDeals();
      await get().loadPriceAlerts();
      
      set({ isLoading: false });
    } catch (error) {
      console.error('Failed to initialize services:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to initialize services',
        isLoading: false 
      });
    }
  },

  setActiveTab: (tab) => {
    set({ activeTab: tab });
  },

  setUserLocation: (location) => {
    set({ userLocation: location });
  },

  searchFlights: async (criteria) => {
    set({ isSearching: true, error: null, flightSearchCriteria: criteria });
    
    try {
      const { travelService } = get();
      const result = await travelService.searchFlights(criteria);
      
      set({ 
        flightOffers: result.offers,
        isSearching: false 
      });
      
      // Store search key for price tracking
      if (result.searchKey) {
        set(state => ({
          priceHistory: {
            ...state.priceHistory,
            [result.searchKey]: result.offers
          }
        }));
      }
      
    } catch (error) {
      console.error('Flight search failed:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Flight search failed',
        isSearching: false,
        flightOffers: []
      });
    }
  },

  searchHotels: async (criteria) => {
    set({ isSearching: true, error: null, hotelSearchCriteria: criteria });
    
    try {
      const { travelService } = get();
      const result = await travelService.searchHotels(criteria);
      
      set({ 
        hotelOffers: result.offers,
        isSearching: false 
      });
      
      // Store search key for price tracking
      if (result.searchKey) {
        set(state => ({
          priceHistory: {
            ...state.priceHistory,
            [result.searchKey]: result.offers
          }
        }));
      }
      
    } catch (error) {
      console.error('Hotel search failed:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Hotel search failed',
        isSearching: false,
        hotelOffers: []
      });
    }
  },

  searchDestinations: async (query) => {
    if (!query.trim()) {
      set({ searchLocations: [] });
      return;
    }
    
    try {
      const { travelService } = get();
      const locations = await travelService.searchDestinations(query);
      set({ searchLocations: locations });
    } catch (error) {
      console.error('Destination search failed:', error);
      set({ searchLocations: [] });
    }
  },

  loadLocalDeals: async () => {
    try {
      const { travelService } = get();
      const deals = await travelService.getLocalDeals();
      set({ localDeals: deals });
    } catch (error) {
      console.error('Failed to load local deals:', error);
      set({ localDeals: [] });
    }
  },

  refreshDeals: async () => {
    set({ isLoading: true });
    await get().loadLocalDeals();
    set({ isLoading: false });
  },

  createPriceAlert: async (type, criteria, targetPrice) => {
    try {
      const { travelService } = get();
      const alertId = await travelService.createPriceAlert(type, criteria, targetPrice);
      
      // Reload alerts to get the updated list
      await get().loadPriceAlerts();
      
      return alertId;
    } catch (error) {
      console.error('Failed to create price alert:', error);
      throw error;
    }
  },

  loadPriceAlerts: async () => {
    try {
      // This would typically load from the background service
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage(
          { type: 'GET_PRICE_ALERTS' },
          (response) => {
            if (response?.success) {
              set({ priceAlerts: response.data });
            }
          }
        );
      }
    } catch (error) {
      console.error('Failed to load price alerts:', error);
    }
  },

  deletePriceAlert: async (alertId) => {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage(
          { type: 'DELETE_PRICE_ALERT', data: { alertId } },
          (response) => {
            if (response?.success) {
              // Remove from local state
              set(state => ({
                priceAlerts: state.priceAlerts.filter(alert => alert.id !== alertId)
              }));
            }
          }
        );
      }
    } catch (error) {
      console.error('Failed to delete price alert:', error);
      throw error;
    }
  },

  analyzePriceTrends: async (searchKey) => {
    try {
      const { travelService } = get();
      const trends = await travelService.getPriceTrends(searchKey);
      
      set(state => ({
        priceTrends: {
          ...state.priceTrends,
          [searchKey]: trends
        }
      }));
    } catch (error) {
      console.error('Failed to analyze price trends:', error);
    }
  },

  getPricePredictions: async (searchKey, daysAhead = 30) => {
    try {
      const { travelService } = get();
      return await travelService.getPricePredictions(searchKey, daysAhead);
    } catch (error) {
      console.error('Failed to get price predictions:', error);
      return null;
    }
  },

  trackAffiliateClick: async (partner, offerType, offerId) => {
    try {
      const { travelService } = get();
      await travelService.trackAffiliateClick(partner, offerType, offerId);
    } catch (error) {
      console.error('Failed to track affiliate click:', error);
    }
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading) => {
    set({ isLoading: loading });
  }
}));

// Utility hooks for specific data
export const useFlightOffers = () => useTravelStore(state => state.flightOffers);
export const useHotelOffers = () => useTravelStore(state => state.hotelOffers);
export const useLocalDeals = () => useTravelStore(state => state.localDeals);
export const usePriceAlerts = () => useTravelStore(state => state.priceAlerts);
export const useUserLocation = () => useTravelStore(state => state.userLocation);
export const useIsLoading = () => useTravelStore(state => state.isLoading);
export const useIsSearching = () => useTravelStore(state => state.isSearching);
export const useError = () => useTravelStore(state => state.error);
export const useActiveTab = () => useTravelStore(state => state.activeTab);

// Action hooks
export const useTravelActions = () => useTravelStore(state => ({
  initializeServices: state.initializeServices,
  setActiveTab: state.setActiveTab,
  searchFlights: state.searchFlights,
  searchHotels: state.searchHotels,
  searchDestinations: state.searchDestinations,
  loadLocalDeals: state.loadLocalDeals,
  refreshDeals: state.refreshDeals,
  createPriceAlert: state.createPriceAlert,
  deletePriceAlert: state.deletePriceAlert,
  trackAffiliateClick: state.trackAffiliateClick,
  clearError: state.clearError
}));
