import React, { useState } from 'react';
import { X, User, Settings } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ProfilePanel } from './ProfilePanel';
import { SettingsPanel } from './SettingsPanel';
export function ExtensionHeader() {
  const [showProfile, setShowProfile] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  return <>
      <div className="bg-[#FF385C] p-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-1">
            <motion.img src="/logo_icon_mark-darkbg-ref.png" alt="TravelSaver Logo" className="h-8 w-auto object-contain object-center" initial={{
            scale: 0.5,
            opacity: 0
          }} animate={{
            scale: 1,
            opacity: 1
          }} transition={{
            type: 'spring',
            duration: 0.5
          }} />
            <motion.span className="text-white font-bold text-lg" initial={{
            x: -20,
            opacity: 0
          }} animate={{
            x: 0,
            opacity: 1
          }} transition={{
            duration: 0.3
          }}>
              TravelSaver
            </motion.span>
          </div>
          <div className="flex items-center">
            <div className="flex items-center gap-2 mr-4 border-r border-white/20 pr-4">
              <motion.button whileHover={{
              scale: 1.1
            }} whileTap={{
              scale: 0.95
            }} className="text-white hover:bg-white/10 rounded-full p-1.5" onClick={() => setShowSettings(true)}>
                <Settings strokeWidth={2.2} size={20} />
              </motion.button>
              <motion.button whileHover={{
              scale: 1.1
            }} whileTap={{
              scale: 0.95
            }} className="text-white hover:bg-white/10 rounded-full p-1.5" onClick={() => setShowProfile(true)}>
                <User strokeWidth={2.2} size={20} />
              </motion.button>
            </div>
            <motion.button whileHover={{
            scale: 1.1
          }} whileTap={{
            scale: 0.95
          }} className="text-white hover:bg-white/10 rounded-full p-1.5">
              <X strokeWidth={2.2} size={20} />
            </motion.button>
          </div>
        </div>
      </div>
      <AnimatePresence>
        {showProfile && <ProfilePanel onClose={() => setShowProfile(false)} />}
        {showSettings && <SettingsPanel onClose={() => setShowSettings(false)} />}
      </AnimatePresence>
    </>;
}