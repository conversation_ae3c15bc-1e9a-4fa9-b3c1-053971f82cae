// Chrome Extension Background Service Worker
import { PriceTrackingService } from './services/price-tracking-service';
import { GeolocationService } from './services/geolocation-service';
import { AffiliateService } from './services/affiliate-service';

class BackgroundService {
  private priceTracker: PriceTrackingService;
  private geolocationService: GeolocationService;
  private affiliateService: AffiliateService;
  private isInitialized = false;

  constructor() {
    this.init();
  }

  private async init() {
    try {
      // Initialize services
      this.priceTracker = new PriceTrackingService();
      this.geolocationService = new GeolocationService();
      
      // Initialize affiliate service with default config
      this.affiliateService = new AffiliateService({
        bookingComId: 'your-booking-affiliate-id',
        expediaId: 'your-expedia-affiliate-id',
        hotelsComId: 'your-hotels-affiliate-id',
        trackingParams: {
          utm_source: 'travelsaver_extension',
          utm_medium: 'browser_extension',
          utm_campaign: 'travel_deals'
        }
      });

      this.isInitialized = true;
      console.log('TravelSaver background service initialized');
      
      // Set up periodic tasks
      this.setupPeriodicTasks();
      
    } catch (error) {
      console.error('Failed to initialize background service:', error);
    }
  }

  private setupPeriodicTasks() {
    // Check for price updates every 30 minutes
    chrome.alarms.create('priceCheck', { periodInMinutes: 30 });
    
    // Update user location daily
    chrome.alarms.create('locationUpdate', { periodInMinutes: 1440 });
    
    // Clean up old data weekly
    chrome.alarms.create('dataCleanup', { periodInMinutes: 10080 });
  }

  async handleAlarm(alarm: chrome.alarms.Alarm) {
    if (!this.isInitialized) return;

    switch (alarm.name) {
      case 'priceCheck':
        await this.checkPriceAlerts();
        break;
      case 'locationUpdate':
        await this.updateUserLocation();
        break;
      case 'dataCleanup':
        await this.cleanupOldData();
        break;
    }
  }

  private async checkPriceAlerts() {
    try {
      const alerts = await this.priceTracker.getUserAlerts();
      console.log(`Checking ${alerts.length} price alerts`);
      
      // Price checking is handled automatically by PriceTrackingService
      // This is just for logging and monitoring
    } catch (error) {
      console.error('Failed to check price alerts:', error);
    }
  }

  private async updateUserLocation() {
    try {
      const location = await this.geolocationService.getCurrentLocation();
      await this.geolocationService.saveUserLocation(location);
      console.log('User location updated:', location);
    } catch (error) {
      console.error('Failed to update user location:', error);
    }
  }

  private async cleanupOldData() {
    try {
      // Clean up old price history (keep last 90 days)
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 90);
      
      // This would be implemented in the price tracking service
      console.log('Cleaning up data older than:', cutoffDate);
    } catch (error) {
      console.error('Failed to cleanup old data:', error);
    }
  }

  async handleMessage(
    message: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ) {
    if (!this.isInitialized) {
      sendResponse({ error: 'Service not initialized' });
      return;
    }

    try {
      switch (message.type) {
        case 'GET_LOCATION':
          const location = await this.geolocationService.getCurrentLocation();
          sendResponse({ success: true, data: location });
          break;

        case 'CREATE_PRICE_ALERT':
          const alertId = await this.priceTracker.createPriceAlert(
            message.data.type,
            message.data.criteria,
            message.data.targetPrice,
            message.data.userId
          );
          sendResponse({ success: true, data: { alertId } });
          break;

        case 'GET_PRICE_ALERTS':
          const alerts = await this.priceTracker.getUserAlerts(message.data.userId);
          sendResponse({ success: true, data: alerts });
          break;

        case 'DELETE_PRICE_ALERT':
          await this.priceTracker.deletePriceAlert(message.data.alertId);
          sendResponse({ success: true });
          break;

        case 'GET_PRICE_HISTORY':
          const history = await this.priceTracker.getPriceHistory(message.data.searchKey);
          sendResponse({ success: true, data: history });
          break;

        case 'ANALYZE_PRICE_TRENDS':
          const trends = await this.priceTracker.analyzePriceTrends(message.data.searchKey);
          sendResponse({ success: true, data: trends });
          break;

        case 'PREDICT_PRICES':
          const predictions = await this.priceTracker.predictPrices(
            message.data.searchKey,
            message.data.daysAhead
          );
          sendResponse({ success: true, data: predictions });
          break;

        case 'TRACK_AFFILIATE_CLICK':
          await this.affiliateService.trackClick(
            message.data.partner,
            message.data.offerType,
            message.data.offerId,
            message.data.userId
          );
          sendResponse({ success: true });
          break;

        case 'GENERATE_AFFILIATE_URL':
          const url = this.affiliateService.generateDeepLink(
            message.data.partner,
            message.data.offer,
            message.data.type
          );
          sendResponse({ success: true, data: { url } });
          break;

        case 'GET_PERFORMANCE_METRICS':
          const metrics = await this.affiliateService.getPerformanceMetrics(
            message.data.dateRange
          );
          sendResponse({ success: true, data: metrics });
          break;

        default:
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async handleTabUpdate(tabId: number, changeInfo: chrome.tabs.TabChangeInfo, tab: chrome.tabs.Tab) {
    if (changeInfo.status === 'complete' && tab.url) {
      // Check if user is on a travel booking site
      const travelSites = [
        'booking.com',
        'expedia.com',
        'hotels.com',
        'kayak.com',
        'priceline.com'
      ];

      const isOnTravelSite = travelSites.some(site => tab.url!.includes(site));
      
      if (isOnTravelSite) {
        // Inject content script if on travel site
        try {
          await chrome.scripting.executeScript({
            target: { tabId },
            files: ['content.js']
          });
        } catch (error) {
          console.error('Failed to inject content script:', error);
        }
      }
    }
  }

  async handleInstall(details: chrome.runtime.InstalledDetails) {
    if (details.reason === 'install') {
      // First time installation
      console.log('TravelSaver extension installed');
      
      // Set up default settings
      await chrome.storage.local.set({
        settings: {
          notifications: true,
          priceAlerts: true,
          locationTracking: true,
          affiliateTracking: true
        },
        onboardingCompleted: false
      });

      // Open onboarding page
      chrome.tabs.create({
        url: chrome.runtime.getURL('index.html?onboarding=true')
      });
      
    } else if (details.reason === 'update') {
      console.log('TravelSaver extension updated');
    }
  }
}

// Initialize background service
const backgroundService = new BackgroundService();

// Event listeners
chrome.runtime.onInstalled.addListener((details) => {
  backgroundService.handleInstall(details);
});

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  backgroundService.handleMessage(message, sender, sendResponse);
  return true; // Keep message channel open for async response
});

chrome.alarms.onAlarm.addListener((alarm) => {
  backgroundService.handleAlarm(alarm);
});

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  backgroundService.handleTabUpdate(tabId, changeInfo, tab);
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This will open the popup automatically due to manifest configuration
  console.log('Extension icon clicked');
});

// Notification click handler
chrome.notifications.onClicked.addListener((notificationId) => {
  // Open extension popup when notification is clicked
  chrome.action.openPopup();
});

// Context menu setup
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'searchDeals',
    title: 'Search TravelSaver deals for "%s"',
    contexts: ['selection']
  });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'searchDeals' && info.selectionText) {
    // Send message to popup to search for selected text
    chrome.runtime.sendMessage({
      type: 'SEARCH_DEALS',
      query: info.selectionText
    });
  }
});

export {};
