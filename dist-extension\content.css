/* TravelSaver Content Script Styles */

/* Badge styles */
.travelsaver-badge {
  position: relative !important;
  display: inline-block !important;
  background: linear-gradient(135deg, #FF385C, #E0314F) !important;
  color: white !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 10px !important;
  font-weight: bold !important;
  margin-left: 4px !important;
  cursor: pointer !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  z-index: 1000 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  transition: all 0.2s ease !important;
}

.travelsaver-badge:hover {
  background: linear-gradient(135deg, #E0314F, #CC2A44) !important;
  transform: scale(1.05) !important;
}

/* Compare button styles */
.travelsaver-compare-btn {
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  background: #FF385C !important;
  color: white !important;
  border: none !important;
  padding: 6px 12px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  z-index: 1000 !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
  transition: all 0.2s ease !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.travelsaver-compare-btn:hover {
  background: #E0314F !important;
  transform: scale(1.05) !important;
}

/* Overlay styles */
#travelsaver-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(0, 0, 0, 0.8) !important;
  z-index: 10000 !important;
  display: none !important;
  align-items: center !important;
  justify-content: center !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Modal styles */
.travelsaver-modal {
  background: white !important;
  border-radius: 12px !important;
  padding: 24px !important;
  max-width: 500px !important;
  width: 90% !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  position: relative !important;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3) !important;
}

/* Notification styles */
.travelsaver-notification {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: #28a745 !important;
  color: white !important;
  padding: 16px 20px !important;
  border-radius: 8px !important;
  z-index: 10001 !important;
  font-weight: bold !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  animation: slideIn 0.3s ease !important;
}

@keyframes slideIn {
  from {
    transform: translateX(100%) !important;
    opacity: 0 !important;
  }
  to {
    transform: translateX(0) !important;
    opacity: 1 !important;
  }
}

/* Price highlight styles */
.travelsaver-price-highlight {
  background: rgba(255, 56, 92, 0.1) !important;
  border: 2px solid #FF385C !important;
  border-radius: 4px !important;
  padding: 2px !important;
  position: relative !important;
}

.travelsaver-price-highlight::after {
  content: "💰 Better price available!" !important;
  position: absolute !important;
  top: -25px !important;
  left: 0 !important;
  background: #FF385C !important;
  color: white !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 10px !important;
  font-weight: bold !important;
  white-space: nowrap !important;
  z-index: 1001 !important;
}

/* Deal banner styles */
.travelsaver-deal-banner {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: linear-gradient(135deg, #FF385C, #E0314F) !important;
  color: white !important;
  padding: 8px 16px !important;
  text-align: center !important;
  font-weight: bold !important;
  z-index: 9999 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

.travelsaver-deal-banner .close-btn {
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  background: none !important;
  border: none !important;
  color: white !important;
  font-size: 18px !important;
  cursor: pointer !important;
  padding: 0 !important;
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Floating action button */
.travelsaver-fab {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  width: 56px !important;
  height: 56px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #FF385C, #E0314F) !important;
  color: white !important;
  border: none !important;
  cursor: pointer !important;
  z-index: 9998 !important;
  box-shadow: 0 4px 16px rgba(255, 56, 92, 0.4) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 24px !important;
  transition: all 0.3s ease !important;
}

.travelsaver-fab:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(255, 56, 92, 0.6) !important;
}

/* Tooltip styles */
.travelsaver-tooltip {
  position: absolute !important;
  background: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  white-space: nowrap !important;
  z-index: 10002 !important;
  pointer-events: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.travelsaver-tooltip::after {
  content: "" !important;
  position: absolute !important;
  top: 100% !important;
  left: 50% !important;
  margin-left: -5px !important;
  border-width: 5px !important;
  border-style: solid !important;
  border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent !important;
}

/* Loading spinner */
.travelsaver-spinner {
  border: 2px solid #f3f3f3 !important;
  border-top: 2px solid #FF385C !important;
  border-radius: 50% !important;
  width: 20px !important;
  height: 20px !important;
  animation: spin 1s linear infinite !important;
  display: inline-block !important;
  margin-right: 8px !important;
}

@keyframes spin {
  0% { transform: rotate(0deg) !important; }
  100% { transform: rotate(360deg) !important; }
}

/* Price comparison table */
.travelsaver-price-table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 16px 0 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.travelsaver-price-table th,
.travelsaver-price-table td {
  padding: 12px !important;
  text-align: left !important;
  border-bottom: 1px solid #ddd !important;
}

.travelsaver-price-table th {
  background-color: #f8f9fa !important;
  font-weight: bold !important;
  color: #333 !important;
}

.travelsaver-price-table .best-price {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #28a745 !important;
  font-weight: bold !important;
}

.travelsaver-price-table .savings {
  color: #28a745 !important;
  font-weight: bold !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .travelsaver-modal {
    width: 95% !important;
    padding: 16px !important;
    margin: 10px !important;
  }
  
  .travelsaver-compare-btn {
    padding: 4px 8px !important;
    font-size: 10px !important;
  }
  
  .travelsaver-fab {
    width: 48px !important;
    height: 48px !important;
    bottom: 16px !important;
    right: 16px !important;
  }
}

/* Site-specific overrides */
/* Booking.com */
.booking-com .travelsaver-badge {
  margin-left: 8px !important;
}

/* Expedia */
.expedia-com .travelsaver-compare-btn {
  top: 12px !important;
  right: 12px !important;
}

/* Hotels.com */
.hotels-com .travelsaver-price-highlight {
  border-color: #0066CC !important;
}

/* Accessibility */
.travelsaver-badge:focus,
.travelsaver-compare-btn:focus,
.travelsaver-fab:focus {
  outline: 2px solid #FF385C !important;
  outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .travelsaver-badge,
  .travelsaver-compare-btn,
  .travelsaver-fab {
    border: 2px solid white !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .travelsaver-badge,
  .travelsaver-compare-btn,
  .travelsaver-fab,
  .travelsaver-notification {
    transition: none !important;
    animation: none !important;
  }
}
