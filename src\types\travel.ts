// Core travel data types
export interface Location {
  code: string;
  name: string;
  city: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface FlightOffer {
  id: string;
  price: {
    total: number;
    currency: string;
    base?: number;
    fees?: number;
  };
  itineraries: FlightItinerary[];
  travelerPricings: TravelerPricing[];
  lastTicketingDate?: string;
  numberOfBookableSeats?: number;
  validatingAirlineCodes: string[];
}

export interface FlightItinerary {
  duration: string;
  segments: FlightSegment[];
}

export interface FlightSegment {
  departure: {
    iataCode: string;
    terminal?: string;
    at: string;
  };
  arrival: {
    iataCode: string;
    terminal?: string;
    at: string;
  };
  carrierCode: string;
  number: string;
  aircraft: {
    code: string;
  };
  operating?: {
    carrierCode: string;
  };
  duration: string;
  id: string;
  numberOfStops: number;
  blacklistedInEU: boolean;
}

export interface TravelerPricing {
  travelerId: string;
  fareOption: string;
  travelerType: string;
  price: {
    currency: string;
    total: string;
    base: string;
  };
  fareDetailsBySegment: FareDetailsBySegment[];
}

export interface FareDetailsBySegment {
  segmentId: string;
  cabin: string;
  fareBasis: string;
  class: string;
  includedCheckedBags: {
    quantity: number;
  };
}

export interface HotelOffer {
  id: string;
  name: string;
  location: {
    address: string;
    city: string;
    country: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  rating: number;
  reviewScore?: number;
  reviewCount?: number;
  price: {
    total: number;
    currency: string;
    perNight: number;
  };
  checkIn: string;
  checkOut: string;
  nights: number;
  images: string[];
  amenities: string[];
  description?: string;
  cancellationPolicy?: string;
  affiliateUrl?: string;
}

export interface PriceHistory {
  date: string;
  price: number;
  currency: string;
  source: string;
}

export interface PriceAlert {
  id: string;
  userId?: string;
  type: 'flight' | 'hotel';
  criteria: FlightSearchCriteria | HotelSearchCriteria;
  targetPrice: number;
  currentPrice: number;
  isActive: boolean;
  createdAt: string;
  lastChecked: string;
  notifications: PriceNotification[];
}

export interface PriceNotification {
  id: string;
  alertId: string;
  type: 'price_drop' | 'price_increase' | 'deal_found';
  oldPrice: number;
  newPrice: number;
  savings: number;
  message: string;
  timestamp: string;
  isRead: boolean;
}

export interface FlightSearchCriteria {
  origin: string;
  destination: string;
  departureDate: string;
  returnDate?: string;
  adults: number;
  children?: number;
  infants?: number;
  travelClass: 'ECONOMY' | 'PREMIUM_ECONOMY' | 'BUSINESS' | 'FIRST';
  nonStop?: boolean;
  maxPrice?: number;
}

export interface HotelSearchCriteria {
  destination: string;
  checkIn: string;
  checkOut: string;
  adults: number;
  children?: number;
  rooms: number;
  minRating?: number;
  maxPrice?: number;
  amenities?: string[];
}

export interface UserLocation {
  latitude: number;
  longitude: number;
  city?: string;
  country?: string;
  countryCode?: string;
  timezone?: string;
}

export interface Deal {
  id: string;
  type: 'flight' | 'hotel';
  title: string;
  description: string;
  originalPrice: number;
  discountedPrice: number;
  savings: number;
  savingsPercentage: number;
  validUntil: string;
  location: string;
  imageUrl?: string;
  affiliateUrl: string;
  isExclusive: boolean;
  tags: string[];
}

export interface AffiliateConfig {
  bookingComId: string;
  expediaId?: string;
  hotelsComId?: string;
  trackingParams: Record<string, string>;
}

export interface ApiResponse<T> {
  data: T;
  meta?: {
    count: number;
    links?: {
      self?: string;
      next?: string;
      previous?: string;
      last?: string;
      first?: string;
    };
  };
  warnings?: Array<{
    code: number;
    title: string;
    detail: string;
    source?: {
      pointer?: string;
      parameter?: string;
    };
  }>;
}
