{"name": "magic-patterns-vite-template", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "lucide-react": "^0.441.0", "framer-motion": "^11.5.4", "react-feather": "^2.0.10", "clsx": "latest", "tailwind-merge": "latest"}, "devDependencies": {"@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "typescript": "^5.5.4", "vite": "^5.2.0", "tailwindcss": "3.4.17", "autoprefixer": "latest", "postcss": "latest"}}