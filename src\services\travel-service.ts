import { AmadeusAPI } from './amadeus-api';
import { GeolocationService } from './geolocation-service';
import { PriceTrackingService } from './price-tracking-service';
import { AffiliateService } from './affiliate-service';
import { 
  FlightOffer, 
  HotelOffer, 
  FlightSearchCriteria, 
  HotelSearchCriteria,
  Deal,
  UserLocation,
  Location
} from '../types/travel';

export class TravelService {
  private amadeusAPI: AmadeusAPI | null = null;
  private geolocationService: GeolocationService;
  private priceTracker: PriceTrackingService;
  private affiliateService: AffiliateService;
  private isInitialized = false;

  constructor() {
    this.geolocationService = new GeolocationService();
    this.priceTracker = new PriceTrackingService();
    
    // Initialize affiliate service with default config
    this.affiliateService = new AffiliateService({
      bookingComId: process.env.BOOKING_AFFILIATE_ID || 'your-booking-affiliate-id',
      expediaId: process.env.EXPEDIA_AFFILIATE_ID || 'your-expedia-affiliate-id',
      hotelsComId: process.env.HOTELS_AFFILIATE_ID || 'your-hotels-affiliate-id',
      trackingParams: {
        utm_source: 'travelsaver_extension',
        utm_medium: 'browser_extension',
        utm_campaign: 'travel_deals'
      }
    });
  }

  async initialize(amadeusClientId?: string, amadeusClientSecret?: string): Promise<void> {
    try {
      if (amadeusClientId && amadeusClientSecret) {
        this.amadeusAPI = new AmadeusAPI(amadeusClientId, amadeusClientSecret);
      }
      
      this.isInitialized = true;
      console.log('TravelService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize TravelService:', error);
      throw error;
    }
  }

  async searchFlights(criteria: FlightSearchCriteria): Promise<{
    offers: FlightOffer[];
    searchKey: string;
    affiliateUrls: Record<string, string>;
  }> {
    if (!this.amadeusAPI) {
      throw new Error('Amadeus API not initialized');
    }

    try {
      const offers = await this.amadeusAPI.searchFlights(criteria);
      const searchKey = this.generateSearchKey('flight', criteria);
      
      // Track prices for the best offers
      const bestOffers = offers.slice(0, 5);
      for (const offer of bestOffers) {
        await this.priceTracker.addPriceHistory(
          `${searchKey}_${offer.id}`,
          offer.price.total,
          offer.price.currency,
          'amadeus'
        );
      }

      // Generate affiliate URLs for the best offer
      const affiliateUrls: Record<string, string> = {};
      if (bestOffers.length > 0) {
        const bestOffer = bestOffers[0];
        affiliateUrls.booking = this.affiliateService.generateDeepLink('booking', bestOffer, 'flight');
        affiliateUrls.expedia = this.affiliateService.generateDeepLink('expedia', bestOffer, 'flight');
      }

      return {
        offers,
        searchKey,
        affiliateUrls
      };
    } catch (error) {
      console.error('Flight search failed:', error);
      throw new Error('Failed to search flights');
    }
  }

  async searchHotels(criteria: HotelSearchCriteria): Promise<{
    offers: HotelOffer[];
    searchKey: string;
    affiliateUrls: Record<string, string>;
  }> {
    if (!this.amadeusAPI) {
      throw new Error('Amadeus API not initialized');
    }

    try {
      const offers = await this.amadeusAPI.searchHotels(criteria);
      const searchKey = this.generateSearchKey('hotel', criteria);
      
      // Track prices for the best offers
      const bestOffers = offers.slice(0, 5);
      for (const offer of bestOffers) {
        await this.priceTracker.addPriceHistory(
          `${searchKey}_${offer.id}`,
          offer.price.total,
          offer.price.currency,
          'amadeus'
        );
      }

      // Generate affiliate URLs for the best offer
      const affiliateUrls: Record<string, string> = {};
      if (bestOffers.length > 0) {
        const bestOffer = bestOffers[0];
        affiliateUrls.booking = this.affiliateService.generateDeepLink('booking', bestOffer, 'hotel');
        affiliateUrls.expedia = this.affiliateService.generateDeepLink('expedia', bestOffer, 'hotel');
        affiliateUrls.hotels = this.affiliateService.generateDeepLink('hotels', bestOffer, 'hotel');
      }

      return {
        offers,
        searchKey,
        affiliateUrls
      };
    } catch (error) {
      console.error('Hotel search failed:', error);
      throw new Error('Failed to search hotels');
    }
  }

  async getLocalDeals(radiusKm: number = 100): Promise<Deal[]> {
    try {
      const userLocation = await this.geolocationService.getCurrentLocation();
      const nearbyAirports = await this.geolocationService.getNearbyAirports(userLocation, radiusKm);
      
      const deals: Deal[] = [];
      
      // Search for flight deals from nearby airports
      for (const airport of nearbyAirports.slice(0, 3)) { // Limit to 3 nearest airports
        try {
          const flightDeals = await this.searchPopularDestinations(airport.code);
          deals.push(...flightDeals);
        } catch (error) {
          console.warn(`Failed to get deals from ${airport.code}:`, error);
        }
      }

      // Search for hotel deals in user's city
      if (userLocation.city) {
        try {
          const hotelDeals = await this.searchLocalHotels(userLocation.city);
          deals.push(...hotelDeals);
        } catch (error) {
          console.warn(`Failed to get hotel deals for ${userLocation.city}:`, error);
        }
      }

      return deals.sort((a, b) => b.savingsPercentage - a.savingsPercentage).slice(0, 20);
    } catch (error) {
      console.error('Failed to get local deals:', error);
      return [];
    }
  }

  async searchDestinations(query: string): Promise<Location[]> {
    if (!this.amadeusAPI) {
      // Fallback to geolocation service
      try {
        const locations = await this.geolocationService.getLocationFromQuery(query);
        return locations.map(loc => ({
          code: loc.city?.substring(0, 3).toUpperCase() || 'UNK',
          name: loc.city || 'Unknown',
          city: loc.city || '',
          country: loc.country || '',
          coordinates: {
            latitude: loc.latitude,
            longitude: loc.longitude
          }
        }));
      } catch (error) {
        console.error('Destination search fallback failed:', error);
        return [];
      }
    }

    try {
      return await this.amadeusAPI.searchLocations(query);
    } catch (error) {
      console.error('Destination search failed:', error);
      return [];
    }
  }

  async getPriceTrends(searchKey: string): Promise<{
    trend: 'increasing' | 'decreasing' | 'stable';
    averagePrice: number;
    lowestPrice: number;
    highestPrice: number;
    priceChange24h: number;
    priceChange7d: number;
    recommendation: string;
  }> {
    return await this.priceTracker.analyzePriceTrends(searchKey);
  }

  async getPricePredictions(searchKey: string, daysAhead: number = 30): Promise<{
    predictions: Array<{ date: string; predictedPrice: number; confidence: number }>;
    recommendation: string;
  }> {
    return await this.priceTracker.predictPrices(searchKey, daysAhead);
  }

  async createPriceAlert(
    type: 'flight' | 'hotel',
    criteria: FlightSearchCriteria | HotelSearchCriteria,
    targetPrice: number,
    userId?: string
  ): Promise<string> {
    return await this.priceTracker.createPriceAlert(type, criteria, targetPrice, userId);
  }

  async getUserLocation(): Promise<UserLocation> {
    return await this.geolocationService.getCurrentLocation();
  }

  async trackAffiliateClick(
    partner: string,
    offerType: 'hotel' | 'flight',
    offerId: string,
    userId?: string
  ): Promise<void> {
    await this.affiliateService.trackClick(partner, offerType, offerId, userId);
  }

  async getAffiliatePerformance(dateRange: { start: string; end: string }): Promise<{
    clicks: number;
    conversions: number;
    conversionRate: number;
    totalEarnings: number;
    partnerBreakdown: Record<string, any>;
  }> {
    return await this.affiliateService.getPerformanceMetrics(dateRange);
  }

  private async searchPopularDestinations(originCode: string): Promise<Deal[]> {
    const popularDestinations = [
      'LAX', 'JFK', 'LHR', 'CDG', 'NRT', 'SYD', 'DXB', 'SIN'
    ].filter(dest => dest !== originCode);

    const deals: Deal[] = [];
    const today = new Date();
    const futureDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days ahead

    for (const destination of popularDestinations.slice(0, 5)) {
      try {
        const criteria: FlightSearchCriteria = {
          origin: originCode,
          destination,
          departureDate: futureDate.toISOString().split('T')[0],
          adults: 1,
          travelClass: 'ECONOMY'
        };

        const result = await this.searchFlights(criteria);
        if (result.offers.length > 0) {
          const bestOffer = result.offers[0];
          const originalPrice = bestOffer.price.total * 1.2; // Simulate original price
          
          deals.push({
            id: `flight_${bestOffer.id}`,
            type: 'flight',
            title: `${originCode} → ${destination}`,
            description: `Round trip flight deal`,
            originalPrice,
            discountedPrice: bestOffer.price.total,
            savings: originalPrice - bestOffer.price.total,
            savingsPercentage: ((originalPrice - bestOffer.price.total) / originalPrice) * 100,
            validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            location: destination,
            affiliateUrl: result.affiliateUrls.booking || '',
            isExclusive: Math.random() > 0.7,
            tags: ['flight', 'international', 'deal']
          });
        }
      } catch (error) {
        console.warn(`Failed to search flights to ${destination}:`, error);
      }
    }

    return deals;
  }

  private async searchLocalHotels(city: string): Promise<Deal[]> {
    const deals: Deal[] = [];
    const today = new Date();
    const checkIn = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000); // 1 week ahead
    const checkOut = new Date(checkIn.getTime() + 2 * 24 * 60 * 60 * 1000); // 2 nights

    try {
      const criteria: HotelSearchCriteria = {
        destination: city,
        checkIn: checkIn.toISOString().split('T')[0],
        checkOut: checkOut.toISOString().split('T')[0],
        adults: 2,
        rooms: 1
      };

      const result = await this.searchHotels(criteria);
      
      result.offers.slice(0, 5).forEach(offer => {
        const originalPrice = offer.price.total * 1.15; // Simulate original price
        
        deals.push({
          id: `hotel_${offer.id}`,
          type: 'hotel',
          title: offer.name,
          description: `${offer.nights} nights in ${offer.location.city}`,
          originalPrice,
          discountedPrice: offer.price.total,
          savings: originalPrice - offer.price.total,
          savingsPercentage: ((originalPrice - offer.price.total) / originalPrice) * 100,
          validUntil: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          location: offer.location.city,
          affiliateUrl: result.affiliateUrls.booking || '',
          isExclusive: Math.random() > 0.8,
          tags: ['hotel', 'local', 'weekend']
        });
      });
    } catch (error) {
      console.warn(`Failed to search hotels in ${city}:`, error);
    }

    return deals;
  }

  private generateSearchKey(type: 'flight' | 'hotel', criteria: any): string {
    if (type === 'flight') {
      const flightCriteria = criteria as FlightSearchCriteria;
      return `flight_${flightCriteria.origin}_${flightCriteria.destination}_${flightCriteria.departureDate}`;
    } else {
      const hotelCriteria = criteria as HotelSearchCriteria;
      return `hotel_${hotelCriteria.destination}_${hotelCriteria.checkIn}_${hotelCriteria.checkOut}`;
    }
  }

  // Utility method to get API status
  getApiStatus(): {
    amadeus: boolean;
    geolocation: boolean;
    priceTracking: boolean;
    affiliate: boolean;
  } {
    return {
      amadeus: this.amadeusAPI !== null,
      geolocation: true,
      priceTracking: true,
      affiliate: true
    };
  }
}
