import { AffiliateConfig, FlightOffer, HotelOffer } from '../types/travel';

export class AffiliateService {
  private config: AffiliateConfig;
  private trackingPixels: Map<string, string> = new Map();

  constructor(config: AffiliateConfig) {
    this.config = config;
    this.initializeTracking();
  }

  private initializeTracking(): void {
    // Initialize tracking pixels and conversion tracking
    this.trackingPixels.set('booking.com', 'https://q-xx.bstatic.com/xdata/images/xphoto/1x1/');
    this.trackingPixels.set('expedia', 'https://www.expedia.com/p/d/');
    this.trackingPixels.set('hotels.com', 'https://www.hotels.com/analytics/');
  }

  generateBookingComUrl(offer: HotelOffer | FlightOffer, type: 'hotel' | 'flight'): string {
    const baseUrl = 'https://www.booking.com';
    const affiliateId = this.config.bookingComId;
    
    if (type === 'hotel' && 'location' in offer) {
      const hotelOffer = offer as HotelOffer;
      const params = new URLSearchParams({
        aid: affiliateId,
        label: 'travelsaver-extension',
        sid: this.generateSessionId(),
        dest_id: this.getDestinationId(hotelOffer.location.city),
        dest_type: 'city',
        checkin: hotelOffer.checkIn,
        checkout: hotelOffer.checkOut,
        group_adults: '2',
        no_rooms: '1',
        group_children: '0',
        utm_source: 'travelsaver',
        utm_medium: 'extension',
        utm_campaign: 'hotel_deals',
        ...this.config.trackingParams
      });

      return `${baseUrl}/searchresults.html?${params.toString()}`;
    }

    // For flights, redirect to Booking.com flights section
    if (type === 'flight' && 'itineraries' in offer) {
      const flightOffer = offer as FlightOffer;
      const firstSegment = flightOffer.itineraries[0]?.segments[0];
      
      if (firstSegment) {
        const params = new URLSearchParams({
          aid: affiliateId,
          label: 'travelsaver-extension-flights',
          sid: this.generateSessionId(),
          from: firstSegment.departure.iataCode,
          to: firstSegment.arrival.iataCode,
          departure_date: firstSegment.departure.at.split('T')[0],
          utm_source: 'travelsaver',
          utm_medium: 'extension',
          utm_campaign: 'flight_deals',
          ...this.config.trackingParams
        });

        return `${baseUrl}/flights?${params.toString()}`;
      }
    }

    return baseUrl;
  }

  generateExpediaUrl(offer: HotelOffer | FlightOffer, type: 'hotel' | 'flight'): string {
    const baseUrl = 'https://www.expedia.com';
    const affiliateId = this.config.expediaId || 'default';
    
    if (type === 'hotel' && 'location' in offer) {
      const hotelOffer = offer as HotelOffer;
      const params = new URLSearchParams({
        AFFID: affiliateId,
        destination: hotelOffer.location.city,
        startDate: hotelOffer.checkIn,
        endDate: hotelOffer.checkOut,
        rooms: '1',
        adults: '2',
        utm_source: 'travelsaver',
        utm_medium: 'extension',
        utm_campaign: 'hotel_comparison'
      });

      return `${baseUrl}/Hotel-Search?${params.toString()}`;
    }

    if (type === 'flight' && 'itineraries' in offer) {
      const flightOffer = offer as FlightOffer;
      const firstSegment = flightOffer.itineraries[0]?.segments[0];
      
      if (firstSegment) {
        const params = new URLSearchParams({
          AFFID: affiliateId,
          flight_type: flightOffer.itineraries.length > 1 ? 'roundtrip' : 'oneway',
          from: firstSegment.departure.iataCode,
          to: firstSegment.arrival.iataCode,
          departure_date: firstSegment.departure.at.split('T')[0],
          utm_source: 'travelsaver',
          utm_medium: 'extension',
          utm_campaign: 'flight_comparison'
        });

        return `${baseUrl}/Flights-Search?${params.toString()}`;
      }
    }

    return baseUrl;
  }

  generateHotelsComUrl(offer: HotelOffer): string {
    const baseUrl = 'https://www.hotels.com';
    const affiliateId = this.config.hotelsComId || 'default';
    
    const params = new URLSearchParams({
      pos: 'HCOM_US',
      locale: 'en_US',
      affid: affiliateId,
      destination: offer.location.city,
      checkin: offer.checkIn,
      checkout: offer.checkOut,
      rooms: '1',
      adults: '2',
      utm_source: 'travelsaver',
      utm_medium: 'extension',
      utm_campaign: 'hotel_deals'
    });

    return `${baseUrl}/search.do?${params.toString()}`;
  }

  async trackClick(
    affiliatePartner: string,
    offerType: 'hotel' | 'flight',
    offerId: string,
    userId?: string
  ): Promise<void> {
    const trackingData = {
      partner: affiliatePartner,
      offerType,
      offerId,
      userId,
      timestamp: new Date().toISOString(),
      sessionId: this.generateSessionId(),
      userAgent: navigator.userAgent,
      referrer: document.referrer
    };

    try {
      // Store tracking data locally
      await this.storeTrackingData(trackingData);
      
      // Send tracking pixel
      await this.fireTrackingPixel(affiliatePartner, trackingData);
      
      // Log for analytics
      console.log('Affiliate click tracked:', trackingData);
    } catch (error) {
      console.error('Failed to track affiliate click:', error);
    }
  }

  async trackConversion(
    affiliatePartner: string,
    offerType: 'hotel' | 'flight',
    offerId: string,
    conversionValue: number,
    currency: string,
    userId?: string
  ): Promise<void> {
    const conversionData = {
      partner: affiliatePartner,
      offerType,
      offerId,
      userId,
      value: conversionValue,
      currency,
      timestamp: new Date().toISOString(),
      sessionId: this.generateSessionId()
    };

    try {
      await this.storeConversionData(conversionData);
      console.log('Affiliate conversion tracked:', conversionData);
    } catch (error) {
      console.error('Failed to track affiliate conversion:', error);
    }
  }

  generateDeepLink(
    partner: 'booking' | 'expedia' | 'hotels',
    offer: HotelOffer | FlightOffer,
    type: 'hotel' | 'flight'
  ): string {
    switch (partner) {
      case 'booking':
        return this.generateBookingComUrl(offer, type);
      case 'expedia':
        return this.generateExpediaUrl(offer, type);
      case 'hotels':
        if (type === 'hotel') {
          return this.generateHotelsComUrl(offer as HotelOffer);
        }
        return this.generateExpediaUrl(offer, type); // Fallback for flights
      default:
        return this.generateBookingComUrl(offer, type);
    }
  }

  async getCommissionRates(): Promise<Record<string, { hotel: number; flight: number }>> {
    // In a real implementation, this would fetch current commission rates from APIs
    return {
      'booking.com': { hotel: 0.04, flight: 0.02 }, // 4% hotels, 2% flights
      'expedia': { hotel: 0.035, flight: 0.025 },
      'hotels.com': { hotel: 0.045, flight: 0 }
    };
  }

  async calculateEstimatedEarnings(
    partner: string,
    offerType: 'hotel' | 'flight',
    bookingValue: number
  ): Promise<number> {
    const rates = await this.getCommissionRates();
    const partnerRates = rates[partner];
    
    if (!partnerRates) return 0;
    
    const rate = offerType === 'hotel' ? partnerRates.hotel : partnerRates.flight;
    return bookingValue * rate;
  }

  async getPerformanceMetrics(dateRange: { start: string; end: string }): Promise<{
    clicks: number;
    conversions: number;
    conversionRate: number;
    totalEarnings: number;
    partnerBreakdown: Record<string, {
      clicks: number;
      conversions: number;
      earnings: number;
    }>;
  }> {
    try {
      const trackingData = await this.getStoredTrackingData(dateRange);
      const conversionData = await this.getStoredConversionData(dateRange);
      
      const clicks = trackingData.length;
      const conversions = conversionData.length;
      const conversionRate = clicks > 0 ? (conversions / clicks) * 100 : 0;
      
      const totalEarnings = conversionData.reduce((sum, conversion) => {
        return sum + (conversion.estimatedEarnings || 0);
      }, 0);

      const partnerBreakdown: Record<string, any> = {};
      
      // Calculate per-partner metrics
      for (const partner of ['booking.com', 'expedia', 'hotels.com']) {
        const partnerClicks = trackingData.filter(t => t.partner === partner).length;
        const partnerConversions = conversionData.filter(c => c.partner === partner).length;
        const partnerEarnings = conversionData
          .filter(c => c.partner === partner)
          .reduce((sum, c) => sum + (c.estimatedEarnings || 0), 0);

        partnerBreakdown[partner] = {
          clicks: partnerClicks,
          conversions: partnerConversions,
          earnings: partnerEarnings
        };
      }

      return {
        clicks,
        conversions,
        conversionRate: Math.round(conversionRate * 100) / 100,
        totalEarnings: Math.round(totalEarnings * 100) / 100,
        partnerBreakdown
      };
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
      return {
        clicks: 0,
        conversions: 0,
        conversionRate: 0,
        totalEarnings: 0,
        partnerBreakdown: {}
      };
    }
  }

  private generateSessionId(): string {
    return `ts_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDestinationId(city: string): string {
    // This would typically use a mapping service or API
    // For now, return a default destination ID
    const cityMappings: Record<string, string> = {
      'New York': '-2601889',
      'London': '-2601889',
      'Paris': '-1456928',
      'Tokyo': '-246227',
      'Sydney': '-1603135'
    };
    
    return cityMappings[city] || '-2601889'; // Default to New York
  }

  private async fireTrackingPixel(partner: string, data: any): Promise<void> {
    const pixelUrl = this.trackingPixels.get(partner);
    if (!pixelUrl) return;

    try {
      const img = new Image();
      img.src = `${pixelUrl}?${new URLSearchParams({
        partner,
        offer_id: data.offerId,
        session_id: data.sessionId,
        timestamp: data.timestamp
      }).toString()}`;
    } catch (error) {
      console.error('Failed to fire tracking pixel:', error);
    }
  }

  private async storeTrackingData(data: any): Promise<void> {
    try {
      const key = 'affiliate_tracking';
      let stored = [];
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get([key]);
        stored = result[key] || [];
      } else {
        const storedJson = localStorage.getItem(key);
        stored = storedJson ? JSON.parse(storedJson) : [];
      }
      
      stored.push(data);
      
      // Keep only last 1000 entries
      if (stored.length > 1000) {
        stored = stored.slice(-1000);
      }
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ [key]: stored });
      } else {
        localStorage.setItem(key, JSON.stringify(stored));
      }
    } catch (error) {
      console.error('Failed to store tracking data:', error);
    }
  }

  private async storeConversionData(data: any): Promise<void> {
    try {
      const key = 'affiliate_conversions';
      let stored = [];
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get([key]);
        stored = result[key] || [];
      } else {
        const storedJson = localStorage.getItem(key);
        stored = storedJson ? JSON.parse(storedJson) : [];
      }
      
      // Calculate estimated earnings
      const estimatedEarnings = await this.calculateEstimatedEarnings(
        data.partner,
        data.offerType,
        data.value
      );
      
      stored.push({ ...data, estimatedEarnings });
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ [key]: stored });
      } else {
        localStorage.setItem(key, JSON.stringify(stored));
      }
    } catch (error) {
      console.error('Failed to store conversion data:', error);
    }
  }

  private async getStoredTrackingData(dateRange: { start: string; end: string }): Promise<any[]> {
    try {
      const key = 'affiliate_tracking';
      let stored = [];
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get([key]);
        stored = result[key] || [];
      } else {
        const storedJson = localStorage.getItem(key);
        stored = storedJson ? JSON.parse(storedJson) : [];
      }
      
      return stored.filter((item: any) => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= new Date(dateRange.start) && itemDate <= new Date(dateRange.end);
      });
    } catch (error) {
      console.error('Failed to get stored tracking data:', error);
      return [];
    }
  }

  private async getStoredConversionData(dateRange: { start: string; end: string }): Promise<any[]> {
    try {
      const key = 'affiliate_conversions';
      let stored = [];
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get([key]);
        stored = result[key] || [];
      } else {
        const storedJson = localStorage.getItem(key);
        stored = storedJson ? JSON.parse(storedJson) : [];
      }
      
      return stored.filter((item: any) => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= new Date(dateRange.start) && itemDate <= new Date(dateRange.end);
      });
    } catch (error) {
      console.error('Failed to get stored conversion data:', error);
      return [];
    }
  }
}
