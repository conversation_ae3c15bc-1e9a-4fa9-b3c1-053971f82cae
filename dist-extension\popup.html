
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TravelSaver</title>
  <style>
    body {
      width: 400px;
      height: 600px;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
    }
    
    .header {
      background: linear-gradient(135deg, #FF385C, #E0314F);
      color: white;
      padding: 16px;
      text-align: center;
    }
    
    .logo {
      width: 32px;
      height: 32px;
      margin-bottom: 8px;
    }
    
    .title {
      font-size: 18px;
      font-weight: bold;
      margin: 0;
    }
    
    .content {
      padding: 20px;
    }
    
    .feature {
      background: white;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .feature h3 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 14px;
    }
    
    .feature p {
      margin: 0;
      color: #666;
      font-size: 12px;
    }
    
    .cta-button {
      background: #FF385C;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      font-weight: bold;
      cursor: pointer;
      width: 100%;
      margin-top: 16px;
    }
    
    .cta-button:hover {
      background: #E0314F;
    }
    
    .status {
      text-align: center;
      padding: 16px;
      background: #e8f5e8;
      color: #2d5a2d;
      border-radius: 6px;
      margin-bottom: 16px;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="header">
    <img src="logo_icon_mark-darkbg-ref.png" alt="TravelSaver" class="logo">
    <h1 class="title">TravelSaver</h1>
  </div>
  
  <div class="content">
    <div class="status">
      ✅ Extension Active - Finding deals on this page
    </div>
    
    <div class="feature">
      <h3>🔍 Smart Price Tracking</h3>
      <p>Monitor flight and hotel prices across multiple providers with real-time alerts.</p>
    </div>
    
    <div class="feature">
      <h3>💰 Affiliate Integration</h3>
      <p>Earn commissions through Booking.com, Expedia, and other travel partners.</p>
    </div>
    
    <div class="feature">
      <h3>📊 Price Analytics</h3>
      <p>View historical trends and future price predictions to book at the best time.</p>
    </div>
    
    <div class="feature">
      <h3>🌍 Global Deals</h3>
      <p>Discover the cheapest flights and hotels worldwide with location-based recommendations.</p>
    </div>
    
    <button class="cta-button" onclick="openFullApp()">
      Open Full TravelSaver App
    </button>
  </div>
  
  <script>
    function openFullApp() {
      chrome.tabs.create({
        url: chrome.runtime.getURL('index.html')
      });
    }
    
    // Check if we're on a travel site
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      const url = tabs[0].url;
      const travelSites = ['booking.com', 'expedia.com', 'hotels.com', 'kayak.com'];
      const isOnTravelSite = travelSites.some(site => url.includes(site));
      
      if (isOnTravelSite) {
        document.querySelector('.status').innerHTML = 
          '🎯 Travel site detected - Enhanced features active';
      }
    });
  </script>
</body>
</html>
