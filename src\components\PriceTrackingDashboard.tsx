import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  Bell, 
  BellOff, 
  Target,
  Calendar,
  DollarSign,
  AlertTriangle
} from 'lucide-react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { usePriceAlerts, useTravelActions } from '../store/travel-store';
import { PriceAlert } from '../types/travel';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface PriceTrackingDashboardProps {
  searchKey?: string;
  currentPrice?: number;
  currency?: string;
}

export function PriceTrackingDashboard({ 
  searchKey, 
  currentPrice, 
  currency = 'USD' 
}: PriceTrackingDashboardProps) {
  const priceAlerts = usePriceAlerts();
  const { createPriceAlert, deletePriceAlert } = useTravelActions();
  
  const [showCreateAlert, setShowCreateAlert] = useState(false);
  const [targetPrice, setTargetPrice] = useState(currentPrice ? Math.round(currentPrice * 0.9) : 0);
  const [priceHistory, setPriceHistory] = useState<any[]>([]);
  const [priceTrends, setPriceTrends] = useState<any>(null);
  const [predictions, setPredictions] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Mock price history data (in real implementation, this would come from the API)
  useEffect(() => {
    if (currentPrice) {
      const mockHistory = generateMockPriceHistory(currentPrice);
      setPriceHistory(mockHistory);
      
      const mockTrends = {
        trend: 'decreasing' as const,
        averagePrice: currentPrice * 1.1,
        lowestPrice: currentPrice * 0.85,
        highestPrice: currentPrice * 1.3,
        priceChange24h: -5.2,
        priceChange7d: -12.8,
        recommendation: 'Great time to book! Prices are trending down.'
      };
      setPriceTrends(mockTrends);

      const mockPredictions = {
        predictions: generateMockPredictions(currentPrice),
        recommendation: 'Prices expected to rise next week. Book soon!'
      };
      setPredictions(mockPredictions);
    }
  }, [currentPrice]);

  const handleCreateAlert = async () => {
    if (!searchKey || !targetPrice) return;
    
    setIsLoading(true);
    try {
      await createPriceAlert('flight', { searchKey }, targetPrice);
      setShowCreateAlert(false);
      setTargetPrice(currentPrice ? Math.round(currentPrice * 0.9) : 0);
    } catch (error) {
      console.error('Failed to create price alert:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAlert = async (alertId: string) => {
    try {
      await deletePriceAlert(alertId);
    } catch (error) {
      console.error('Failed to delete price alert:', error);
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return <TrendingUp className="text-red-500" size={20} />;
      case 'decreasing':
        return <TrendingDown className="text-green-500" size={20} />;
      default:
        return <Minus className="text-gray-500" size={20} />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return 'text-red-500';
      case 'decreasing':
        return 'text-green-500';
      default:
        return 'text-gray-500';
    }
  };

  const chartData = {
    labels: priceHistory.map(item => new Date(item.date).toLocaleDateString()),
    datasets: [
      {
        label: 'Price History',
        data: priceHistory.map(item => item.price),
        borderColor: '#FF385C',
        backgroundColor: 'rgba(255, 56, 92, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#FF385C',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#FF385C',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: (context: any) => `${currency} ${context.parsed.y.toLocaleString()}`
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#6B7280',
          font: {
            size: 12
          }
        }
      },
      y: {
        grid: {
          color: 'rgba(107, 114, 128, 0.1)'
        },
        ticks: {
          color: '#6B7280',
          font: {
            size: 12
          },
          callback: (value: any) => `${currency} ${value.toLocaleString()}`
        }
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-lg p-6"
    >
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-bold text-gray-900">Price Tracking</h3>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setShowCreateAlert(true)}
          className="flex items-center gap-2 bg-[#FF385C] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#E0314F]"
        >
          <Bell size={16} />
          Set Alert
        </motion.button>
      </div>

      {/* Current Price & Trends */}
      {currentPrice && priceTrends && (
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Current Price</span>
              {getTrendIcon(priceTrends.trend)}
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {currency} {currentPrice.toLocaleString()}
            </div>
            <div className={`text-sm ${getTrendColor(priceTrends.trend)}`}>
              {priceTrends.priceChange24h > 0 ? '+' : ''}{priceTrends.priceChange24h.toFixed(1)}% (24h)
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Best Price</span>
              <Target className="text-green-500" size={16} />
            </div>
            <div className="text-2xl font-bold text-green-600">
              {currency} {priceTrends.lowestPrice.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">
              Save {currency} {(currentPrice - priceTrends.lowestPrice).toLocaleString()}
            </div>
          </div>
        </div>
      )}

      {/* Price Chart */}
      {priceHistory.length > 0 && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold mb-3">Price History (Last 30 Days)</h4>
          <div className="h-64">
            <Line data={chartData} options={chartOptions} />
          </div>
        </div>
      )}

      {/* Recommendation */}
      {priceTrends?.recommendation && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="text-blue-500 mt-0.5" size={20} />
            <div>
              <h5 className="font-semibold text-blue-900 mb-1">Recommendation</h5>
              <p className="text-blue-800">{priceTrends.recommendation}</p>
            </div>
          </div>
        </div>
      )}

      {/* Price Predictions */}
      {predictions && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold mb-3">Price Forecast (Next 7 Days)</h4>
          <div className="grid grid-cols-7 gap-2">
            {predictions.predictions.slice(0, 7).map((prediction: any, index: number) => (
              <div key={index} className="text-center p-2 bg-gray-50 rounded">
                <div className="text-xs text-gray-600 mb-1">
                  {new Date(prediction.date).toLocaleDateString('en-US', { weekday: 'short' })}
                </div>
                <div className="text-sm font-semibold">
                  {currency} {prediction.predictedPrice.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">
                  {Math.round(prediction.confidence * 100)}%
                </div>
              </div>
            ))}
          </div>
          <p className="text-sm text-gray-600 mt-2">{predictions.recommendation}</p>
        </div>
      )}

      {/* Active Price Alerts */}
      {priceAlerts.length > 0 && (
        <div>
          <h4 className="text-lg font-semibold mb-3">Active Price Alerts</h4>
          <div className="space-y-3">
            {priceAlerts.map((alert) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <Bell className="text-[#FF385C]" size={16} />
                  <div>
                    <div className="font-medium">
                      Alert when price drops to {currency} {alert.targetPrice.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">
                      Current: {currency} {alert.currentPrice.toLocaleString()}
                    </div>
                  </div>
                </div>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => handleDeleteAlert(alert.id)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <BellOff size={16} />
                </motion.button>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Create Alert Modal */}
      <AnimatePresence>
        {showCreateAlert && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowCreateAlert(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-96 max-w-90vw"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-bold mb-4">Create Price Alert</h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Price ({currency})
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-3 text-gray-400" size={16} />
                  <input
                    type="number"
                    value={targetPrice}
                    onChange={(e) => setTargetPrice(parseInt(e.target.value))}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#FF385C] focus:border-transparent"
                    placeholder="Enter target price"
                  />
                </div>
                {currentPrice && (
                  <p className="text-sm text-gray-600 mt-1">
                    Current price: {currency} {currentPrice.toLocaleString()}
                  </p>
                )}
              </div>

              <div className="flex gap-3">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowCreateAlert(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleCreateAlert}
                  disabled={isLoading || !targetPrice}
                  className="flex-1 px-4 py-2 bg-[#FF385C] text-white rounded-md hover:bg-[#E0314F] disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Creating...' : 'Create Alert'}
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

// Helper functions for mock data
function generateMockPriceHistory(currentPrice: number) {
  const history = [];
  const days = 30;
  
  for (let i = days; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // Generate realistic price variation
    const variation = (Math.random() - 0.5) * 0.2; // ±10% variation
    const trendFactor = (days - i) / days * 0.1; // Slight downward trend
    const price = Math.round(currentPrice * (1 + variation - trendFactor));
    
    history.push({
      date: date.toISOString(),
      price,
      currency: 'USD',
      source: 'amadeus'
    });
  }
  
  return history;
}

function generateMockPredictions(currentPrice: number) {
  const predictions = [];
  
  for (let i = 1; i <= 7; i++) {
    const date = new Date();
    date.setDate(date.getDate() + i);
    
    // Simulate price increase over time
    const increase = i * 0.02; // 2% increase per day
    const variation = (Math.random() - 0.5) * 0.05; // ±2.5% random variation
    const predictedPrice = Math.round(currentPrice * (1 + increase + variation));
    const confidence = Math.max(0.6, 1 - (i / 7) * 0.4); // Decreasing confidence
    
    predictions.push({
      date: date.toISOString().split('T')[0],
      predictedPrice,
      confidence
    });
  }
  
  return predictions;
}
